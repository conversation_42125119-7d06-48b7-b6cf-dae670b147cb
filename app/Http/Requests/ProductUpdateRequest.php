<?php

namespace App\Http\Requests;

use App\Models\ProductRank;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use App\Models\Product;

class ProductUpdateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'product.product_category_id' => 'required',
            'product.name' => 'required',
            'product.short_name' => 'required',
            'product.type' => 'required',
            'product.code' => 'required|unique:m_products,code,' . $this->id,
        ];
        // if ($this->product['code'] != Product::KE_CODE) {
        //     $rules['product_ranks.*.min'] = 'required|max:255';
        //     $rules['product_ranks.*.max'] = 'required|max:255';
        //     $rules['product_ranks.*.year'] = 'required|max:255';
        // }

        return $rules;
    }
    public function attributes()
    {
        return [
            'product.product_category_id' => 'Nhóm sản phẩm',
            'product.code' => 'Mã code sản phẩm',
            'product.name' => 'Tên sản phẩm',
            'product.type' => 'Trạng thái',
            'product.short_name' => 'Tên sản phẩm',
            // 'product_ranks.*.min' => 'Giá nhỏ nhất',
            // 'product_ranks.*.max' => 'Giá lớn nhất',
            // 'product_ranks.*.year' => 'Năm'
        ];
    }
}
