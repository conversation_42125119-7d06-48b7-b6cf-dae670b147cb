<?php

namespace App\Http\Requests;

class SubPropertyStoreRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'sub_property.code' => 'required|unique:sub_properties,code,' . $this->id,
            // 'sub_property.purchase_date' => 'required',
            // 'sub_property.price' => 'required',
        ];
    }
    public function attributes()
    {
        return [
            'sub_property.code' => 'Mã tài sản',
            'sub_property.purchase_date' => 'Ngày mua',
            'sub_property.price' => 'Giá',
        ];
    }
}
