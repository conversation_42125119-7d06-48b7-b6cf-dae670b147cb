<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EditKEContractRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'client_id' => ['required'],
            'origin_ke_sales_packages_id' => ['required', 'uuid'],
            'sale_package_attachment' => ['required', 'array', 'min:1'],
            'sale_package_attachment.*.id' => ['required', 'uuid'],
            'sale_package_attachment.*.code' => ['required', 'max:50'],
            'contract_code' => ['required', 'max:50', 'unique:ke_contracts,contract_code,' . $this->id],
            'attachFiles' => ['array'],
            'attachFiles.*' => 'required|mimes:jpeg,png,jpg,pdf,doc,docx',
            'contract_value' => 'numeric|min:0',
            'payment_amount' => 'numeric|min:0',
            'unequal' => 'numeric|min:0',
            'debt' => 'numeric|min:0',
            'received_money' => 'numeric|min:0',
            'current_payment_amount' => 'numeric|min:0',
            'payment_note' => 'max:500',
            'payment_file' => 'nullable|mimes:jpeg,png,jpg',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'province_id' => 'required',
            'attachedAppendix' => ['required', 'array'],
            'attachedAppendix.*.attached_appendix_name' => ['required', 'max:150'],
            'attachedAppendix.*.attached_appendix_items' => 'array',
            'attachedAppendix.*.attached_appendix_items.*.equipment_type_id' => 'required',
            'attachedAppendix.*.attached_appendix_items.*.amount' => 'numeric',
            'attachedAppendix.*.attached_appendix_items.*.price' => 'numeric',
            'attachedAppendix.*.attached_appendix_items.*.discount' => 'numeric',
            'attachedAppendix.*.attached_appendix_items.*.discount_type' => 'max:10',
            'deletedAttachedAppendix' => 'array|nullable',
            'deletedAttachedAppendixItems' => 'array|nullable',
            'feedbackObject' => 'array|nullable',
            'feedbackObject.*.config_feedback_object_id' => 'required',
            'feedbackObject.*.feedback_amount' => 'required|numeric',
            'feedbackObject.*.feedback_type' => 'required|max:50'
        ];
    }

    public function attributes()
    {
        return [
            'client_id' => 'Khách hàng',
            'origin_ke_sales_packages_id' => 'Gói bán hàng',
            'sale_package_attachment' => 'Tùy chọn gói bán hàng',
            'sale_package_attachment.*.id' => 'Tùy chọn gói bán hàng',
            'sale_package_attachment.*.code' => 'Tùy chọn gói bán hàng',
            'contract_code' => 'Mã hợp đồng',
            'attachFiles' => 'Tệp đính kèm',
            'attachFiles.*' => 'Tệp đính kèm',
            'contract_value' => 'Giá trị hợp đồng',
            'payment_amount' => 'Giá trị thực',
            'unequal' => 'Giá chênh',
            'debt' => 'Công nợ',
            'received_money' => 'Tiền đã thanh toán',
            'current_payment_amount' => 'Số tiền thanh toán',
            'payment_note' => 'Ghi chú thanh toán tiền',
            'payment_file' => 'File đính kèm thanh toán',
            'start_date' => 'Thời gian bắt đầu',
            'end_date' => 'Thời gian hết hạn',
            'province_id' => 'Tỉnh / Thành phố',
            'attachedAppendix' => 'Phụ lục đính kèm',
            'attachedAppendix.*.attached_appendix_name' => 'Tên phụ lục',
            'attachedAppendix.*.attached_appendix_items' => 'Phụ lục',
            'attachedAppendix.*.attached_appendix_items.*.ke_contract_attached_appendix_id' => 'Id phụ lục',
            'attachedAppendix.*.attached_appendix_items.*.equipment_type_id' => 'Tên hàng hóa',
            'attachedAppendix.*.attached_appendix_items.*.amount' => 'Số lượng',
            'attachedAppendix.*.attached_appendix_items.*.price' => 'Giá',
            'attachedAppendix.*.attached_appendix_items.*.discount' => 'Chiết khấu',
            'attachedAppendix.*.attached_appendix_items.*.discount_type' => 'Loại chiết khấu',
            'feedbackObject' => 'Chiết khấu cho đơn vị',
            'feedbackObject.*.config_feedback_object_id' => 'Chiết khấu cho đơn vị',
            'feedbackObject.*.feedback_amount' => 'Chiết khấu cho đơn vị',
            'feedbackObject.*.feedback_type' => 'Chiết khấu cho đơn vị'
        ];
    }
}