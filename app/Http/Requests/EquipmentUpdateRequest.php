<?php

namespace App\Http\Requests;

class EquipmentUpdateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        $rules = [
            'data.equipment_type_id' => 'required',
            'data.warehouse_id' => 'required',
            'data.amount' => 'required',
            'data.price' => 'nullable|numeric',
            'sub_equipments' => 'array'
        ];
        return $rules;
    }
    public function attributes()
    {
        return [
            'data.equipment_type_id' => 'Tên hàng hóa',
            'data.warehouse_id' => 'Tên kho',
            'data.amount' => 'Số lượng',
            'data.price' => 'Đơn giá',
            'sub_equipments' => 'Mã hàng hóa'
        ];
    }
}
