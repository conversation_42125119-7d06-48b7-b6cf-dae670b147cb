<?php

namespace App\Http\Requests;


class ImportContractRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        $rules = [
            'data.*.user_name_product' => 'required|max:30',
            'data.*.school_id' => 'required|max:30|exists:m_schools,school_id',
            'data.*.school_name' => 'required|max:255',
            'data.*.phone_number' => 'required|regex:/(0)[0-9]/|not_regex:/[a-z]/|min:10|max:13',
            'data.*.head_master_name' => 'nullable|max:255',
            'data.*.accountant_name' => 'nullable|max:255',
            'data.*.accountant_phone' => 'nullable|regex:/(0)[0-9]/|not_regex:/[a-z]/|min:10|max:13',
            'data.*.tax_code' => 'nullable|max:20',
            'data.*.contract_code' => 'required|max:20|unique:contracts',
            'data.*.bill_number' => 'nullable|max:20|unique:contracts',
            'data.*.contract_signing_date' => 'required',
            'data.*.contract_expire_date' => 'required',
            'data.*.payment_amount' => 'required|numeric',
            'data.*.contract_value' => 'required|numeric',
            'data.*.unequal' => 'required|numeric',
            'data.*.received_money' => 'required|numeric',
            'data.*.debt' => 'required|numeric',
            'data.*.feedback_so' => 'nullable|numeric',
            'data.*.feedback_phong' => 'nullable|numeric',
            'data.*.feedback_truong' => 'nullable|numeric',
            'product_id' => 'required',
        ];

        return $rules;
    }

    public function attributes()
    {
        return [
            'data.*.user_name_product' => 'Tài khoản hệ thống',
            'data.*.school_id' => 'Mã trường',
            'data.*.school_name' => 'Tên trường',
            'data.*.phone_number' => 'Số điện thoai',
            'data.*.head_master_name' => 'Tên hiệu trưởng',
            'data.*.accountant_name' => 'Tên kế toán',
            'data.*.accountant_phone' => 'SĐT kế toán',
            'data.*.tax_code' => 'Mã số thuế',
            'data.*.contract_code' => 'Mã hợp đồng',
            'data.*.bill_number' => 'Số hóa đơn',
            'data.*.contract_signing_date' => 'Ngày ký hợp đồng',
            'data.*.contract_expire_date' => 'Ngày hết hạn',
            'data.*.contract_value' => 'Giá trị hợp đồng',
            'data.*.payment_amount' => 'Giá trị thực',
            'data.*.unequal' => 'Giá trị chênh lệch',
            'data.*.received_money' => 'Đã thu',
            'data.*.debt' => 'Công nợ',
            'data.*.feedback_so' => 'Feedback Sở',
            'data.*.feedback_phong' => 'Feedback Phòng',
            'data.*.feedback_truong' => 'Feedback Trường',
            'product_id' => 'Tên sản phẩm',
        ];
    }
}
