<?php

namespace App\Http\Requests;

class ExpenseUpdateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'expense.from_date' => 'required',
            'expense.to_date' => 'required',
            'client_selected' => 'required'
        ];
        $rules['array_expenses.*.input_name'] = 'required|max:255';
        $rules['array_expenses.*.amount'] = 'required|numeric|min:1|max:100';
        $rules['array_expenses.*.unit_price'] = 'required|numeric|min:1';
        return $rules;
    }
    public function attributes()
    {
        return [
            'expense.from_date' => 'Từ ngày',
            'expense.to_date' => 'Đến ngày',
            'client_selected' => 'Đối tượng khách hàng không được bỏ trống',
            'array_expenses.*.input_name' => 'K<PERSON>ản chi',
            'array_expenses.*.amount' => 'Số lượng',
            'array_expenses.*.unit_price' => 'Đơn giá',
        ];
    }
}
