<?php

namespace App\Http\Requests;


class ImportEquipmentRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        $rules = [
            'data.*.equipment_code' => 'required|max:30|exists:equipment_types,code',
            'data.*.amount' => 'required|max:30',
            'data.*.sub_equipment' => 'nullable|unique:sub_equipments,code',
            'data.*.price' => 'required|max:30',
            'warehouse_id' => 'required',
        ];

        return $rules;
    }

    public function attributes()
    {
        return [
            'data.*.equipment_code' => 'Mã hàng hoá',
            'data.*.amount' => 'Số lượng',
            'data.*.price' => 'Giá',
            'warehouse_id' => 'Kho hàng hóa',
        ];
    }
}
