<?php

namespace App\Http\Requests;

class ContractUpdateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules =  [
            'province_id' => 'required',
            'district_id' => 'required',
            'company' => 'required',
            'product_id' => 'required',
            'team' => 'required',
            'client_code' => 'required|max:255',
            'tax_code' => 'nullable|max:255',
            'client_name' => 'required|max:255',
            'address' => 'required|max:255',
            'head_master_name' => 'required|max:255',
            'position' => 'required|max:255',
            'head_master_phone' => 'required|max:255',
            'sale_date' => 'required',
            'from_date' => 'required',
            'to_date' => 'required',
            'fb_school' => 'nullable|integer',
            'fb_district' => 'nullable|integer',
            'fb_province' => 'nullable|integer',
            'amount' => 'required|integer|max:2|gt:0',
            'invoice_request' => 'required',
            'contract_value' => 'required|gte:payment_amount|gt:0',
            'payment_amount' => 'required|gt:0',
            'received_money' => 'nullable|lte:contract_value',
            'tax' => 'nullable|integer',
            'vat' => 'nullable|integer',
            'staff_code' => 'required|max:255|exists:users',
        ];
        if ($this->received_money) {
            $rules['payment_date'] = 'required';
        }
        if ($this->payment_date) {
            $rules['received_money'] = 'required|gt:0';
        }
        return $rules;
    }
    public function attributes()
    {
        return [
            'province_id' => 'Tỉnh/Thành phố',
            'district_id' => 'Quận/Huyện',
            'company' => 'Công ty',
            'product_id' => 'Sản phẩm',
            'team' => 'Team',
            'client_code' => 'Mã khách hàng',
            'tax_code' => 'Mã số thuế',
            'client_name' => 'Tên dơn vị',
            'address' => 'Địa chỉ',
            'head_master_name' => 'Tên người đại diện',
            'position' => 'Chức vụ',
            'head_master_phone' => 'SĐT người đại diện',
            'sale_date' => 'Ngày tính doanh số',
            'from_date' => 'Ngày bắt đầu',
            'to_date' => 'Ngày hết hạn',
            'fb_school' => 'Tỷ lệ FB trường',
            'fb_district' => 'Tỷ lệ FB PGD',
            'fb_province' => 'Tỷ lệ FB SGD',
            'amount' => 'Số lượng',
            'invoice_request' => 'Yêu cầu XHĐ',
            'contract_value' => 'Giá trị hợp đồng',
            'payment_amount' => 'Đơn giá thực',
            'received_money' => 'Đã thanh toán',
            'payment_date' => 'Ngày thanh toán',
            'tax' => 'Thuế suất trên giá gửi',
            'vat' => 'Thuế VAT',
            'staff_code' => 'Mã nhân viên',
        ];
    }
}
