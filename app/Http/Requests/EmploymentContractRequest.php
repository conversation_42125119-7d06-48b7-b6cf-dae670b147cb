<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Log;

class EmploymentContractRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rule =  [
            'department_id' => 'required',
            'position_id' => 'required',
            'employment_contract_id' => 'required',
            'employment_contract_number' => 'required',
            'start_date' => 'required',
        ];
        return $rule;
    }
    public function attributes()
    {
        return [
            'department_id' => 'Phòng/bộ phận',
            'position_id' => 'Chức vụ',
            'employment_contract_id' => 'Loại hợp đồng',
            'employment_contract_number' => 'Số hợp đồng',
            'start_date' => 'Ngày bắt đầu',
            'end_date' => 'Ngày kết thúc'
        ];
    }
}
