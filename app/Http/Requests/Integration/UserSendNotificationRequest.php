<?php

namespace App\Http\Requests\Integration;

use App\Http\Requests\BaseRequest;

class UserSendNotificationRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => 'required|string|email|max:255',
            'title' => 'required|string|max:255',
            'body' => 'required|string|max:255',
            'image' => 'string',
            'type' => 'required|in:1,2,3,4,5,6,7'
        ];
    }
}
