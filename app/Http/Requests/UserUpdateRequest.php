<?php

namespace App\Http\Requests;

class UserUpdateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required',
            // 'staff_code' => 'required',
            // 'gender' => 'required',
            // 'phone' => 'required',
            // 'birth_of_date' => 'required',
            // 'id_number' => 'required',
            'end_date_of_work' => 'nullable|after:start_date_of_work',
            'end_date_of_probationary' => 'nullable|after:start_date_of_probationary',

        ];
    }
    public function attributes()
    {
        return [
            'name' => 'Tên nhân viên',
            'staff_code' => 'Mã nhân viên',
            'gender' => 'Giới tính',
            'phone' => 'SĐT cá nhân',
            'birth_of_date' => 'Ngày sinh',
            'id_number' => 'CMT',
            'department_id' => 'Phòng/bộ phận',
            'position_id' => 'Chức vụ',
            'role_id' => 'Nhóm người dùng',
            'employment_contract_id' => 'Loại hợp đồng',
            'employment_contract_number' => 'Số hợp đồng',
            'start_date' => 'Ngày bắt đầu',
            'start_date_of_work' => 'Ngày vào làm việc',
            'end_date_of_work' => 'Ngày nghỉ việc',
            'start_date_of_probationary' => 'Ngày thử việc',
            'end_date_of_probationary' => 'Ngày kết thúc thử việc'
        ];
    }
}
