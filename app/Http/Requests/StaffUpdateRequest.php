<?php

namespace App\Http\Requests;

class StaffUpdateRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|max:255',
            'staff_code' => 'required|max:11',
            'gender' => 'required',
            'phone' => 'required|max:20',
            'phone_vietec' => 'required|max:20',
            'skype_id' => 'required|max:255',
            'email' => 'required|max:255',
            'email_vietec' => 'required|max:255',
            'birth_of_date' => 'required',
            'id_number' => 'required|numeric',
            'place_id_number' => 'required|max:255',
            'date_id_number' => 'required',
            'original_province_id' => 'required',
            'original_district_id' => 'required',
            'original_ward_id' => 'required',
            'original_address' => 'required|max:255',
            'province_id' => 'required',
            'district_id' => 'required',
            'ward_id' => 'required',
            'address' => 'required|max:255',
            'current_province_id' => 'required',
            'current_district_id' => 'required',
            'current_ward_id' => 'required',
            'current_address' => 'required|max:255',
            'necessary_info' => 'required|max:255',
            'necessary_address' => 'required|max:255',
            'bank_number' => 'required|max:255',
            'bank_name' => 'required|max:255'
        ];
    }
    public function attributes()
    {
        return [
            'name' => 'Tên nhân viên',
            'staff_code' => 'Mã nhân viên',
            'gender' => 'Giới tính',
            'phone' => 'SĐT cá nhân',
            'phone_vietec' => 'SĐT vietec',
            'skype_id' => 'Skype Id',
            'email' => 'Email',
            'email_vietec' => 'Email vietec',
            'birth_of_date' => 'Ngày sinh',
            'id_number' => 'CMT',
            'place_id_number' => 'Nơi cấp',
            'date_id_number' => 'Ngày cấp',
            'original_province_id' => 'Tỉnh/thành phố(Nguyên quán)',
            'original_district_id' => 'Quận/huyện(Nguyên quán)',
            'original_ward_id' => 'Phường/xã(Nguyên quán)',
            'original_address' => 'Địa chỉ(Nguyên quán)',
            'province_id' => 'Tỉnh/thành phố(Thường trú)',
            'district_id' => 'Quận/huyện(Thường trú)',
            'ward_id' => 'Phường/xã(Thường trú)',
            'address' => 'Địa chỉ(Thường trú)',
            'current_province_id' => 'Tỉnh/thành phố(ĐC liên hệ)',
            'current_district_id' => 'Quận/huyện(ĐC liên hệ)',
            'current_ward_id' => 'Phường/xã(ĐC liên hệ)',
            'current_address' => 'Địa chỉ(ĐC liên hệ)',
            'necessary_info' => 'Thông tin liên hệ',
            'necessary_address' => 'Địa chỉ liên hệ',
            'bank_number' => 'Số tài khoản ngân hàng',
            'bank_name' => 'Tên ngân hàng'
        ];
    }
}
