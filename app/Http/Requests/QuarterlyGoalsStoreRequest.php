<?php

namespace App\Http\Requests;

class QuarterlyGoalsStoreRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'team_sales_id' => 'required',
            'year' => 'required|unique:quarterly_goals,year,' . $this->year . ',id,team_sales_id,' . $this->team_sales_id,
            'q1_goals' => 'numeric|nullable',
            'q2_goals' => 'numeric|nullable',
            'q3_goals' => 'numeric|nullable',
            'q4_goals' => 'numeric|nullable',
            'new_q1' => 'numeric|nullable',
            'renew_q1' => 'numeric|nullable',
            'new_q2' => 'numeric|nullable',
            'renew_q2' => 'numeric|nullable',
            'new_q3' => 'numeric|nullable',
            'renew_q3' => 'numeric|nullable',
            'new_q4' => 'numeric|nullable',
            'renew_q4' => 'numeric|nullable',
        ];
    }
    public function attributes()
    {
        return [
            'team_sales_id' => 'Tên team kinh doanh',
            'year' => 'Năm',
            'q1_goals' => 'Mục tiêu doanh thu quý 1',
            'q2_goals' => 'Mục tiêu doanh thu quý 2',
            'q3_goals' => 'Mục tiêu doanh thu quý 3',
            'q4_goals' => 'Mục tiêu doanh thu quý 4',
            'new_q1' => 'Ký mới',
            'renew_q1' => 'Tái ký',
            'new_q2' => 'Ký mới',
            'renew_q2' => 'Tái ký',
            'new_q3' => 'Ký mới',
            'renew_q3' => 'Tái ký',
            'new_q4' => 'Ký mới',
            'renew_q4' => 'Tái ký',
        ];
    }
}
