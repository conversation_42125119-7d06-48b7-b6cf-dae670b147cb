<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Http\Middleware\BaseMiddleware;

class MobileAuthMiddleware extends BaseMiddleware
{

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            $this->auth->parseToken()->authenticate();
            if (Auth::guard('api')->check()) {
                return $next($request);
            }

            return response()->json(['error' => __('Vui lòng đăng nhập hệ thống!')], 401);
        } catch (JWTException $e) {
            if ($e instanceof TokenInvalidException) {
                return response()->json(['error' => __('messages.errors.auth.token.invalid')], 401);
            } else if ($e instanceof TokenExpiredException) {
                return response()->json(['error' => __('Lượt đăng nhập hết hạn, vui lòng đăng nhập lại hệ thống!')], 401);
            } else {
                return response()->json(['error' => __('Có lỗi trong quá trình xử lý!')], 401);
            }
        }
    }
}