<?php

namespace App\Http\Controllers;

use App\Http\Requests\CallCenterUpdateRequest;
use App\Models\CallCenter;
use Illuminate\Http\Request;
use App\Models\Response;
use Illuminate\Support\Facades\Log;

class CallCenterController extends BaseController
{
    /**
     * Danh sách call center
     */
    public function index(Request $request)
    {
        $query = CallCenter::orderBy('CreatedDateTime', 'DESC');
        if ($request->RemoteNo) {
            $query->where('RemoteNo', $request->RemoteNo);
        }
        if ($request->FullName) {
            $query->where('FullName', $request->FullName);
        }
        if ($request->SIP) {
            $query->where('SIP', $request->SIP);
        }
        if ($request->CreatedDateTime) {
            $query->whereDate('CreatedDateTime', $request->CreatedDateTime);
        }
        $data = $query->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function chooseClient(CallCenterUpdateRequest $request)
    {
        $data = [
            'client_id' => $request->client_id,
            'product_id' => $request->product_id,
            'user_name_product' => $request->user_name_product
        ];
        CallCenter::where('id', $request->id)->update($data);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
    public function showClient(Request $request)
    {
        $data = CallCenter::where('id', $request->id)->with('client.school', 'product')->first();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
