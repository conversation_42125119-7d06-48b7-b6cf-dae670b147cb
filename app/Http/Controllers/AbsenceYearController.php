<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AbsenceYear;
use App\Models\UserWorking;
use App\Models\Response;
use App\Models\LogAbsenceYear;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use App\Transformer\APIJsonResponse;
use App\Transformer\AbsenceYearTransformer;
class AbsenceYearController extends Controller
{
    /**
     * Display a listing of the resource.
     * 
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = [];
        $departmentId = $request->department_id ?? null;
        $rank_code = $request->rank_code ?? null;
        $keyword = $request->keyword ?? null;

        $model = AbsenceYear::with(
            [
                'user:id,name,staff_code',
                'user.user.department:id,code',
                'user.user.position:id,code',
                'logAbsenceYear', 
                'user.endOfWorking'
            ])
            ->whereHas('user.endOfWorking', function ($q) {
                $q->active();
            })
            ->when($departmentId, function ($query, $departmentId) {
                $query->whereHas('user.user.department', function ($subQuery) use ($departmentId) {
                    return $subQuery->where('id',  $departmentId);
                });
            })
            ->when($rank_code, function ($query, $rank_code) {
                $query->whereHas('user.user.position', function ($subQuery) use ($rank_code) {
                    return $subQuery->where('code',  $rank_code);
                });
            })
            ->when($keyword, function ($query, $keyword) {
                $query->whereHas('user', function ($subQuery) use ($keyword) {
                    return $subQuery->where('name', 'like', '%' . $keyword . '%')
                        ->orWhere('staff_code', 'like', '%' . $keyword . '%');
                });
            });
        $data = $model->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function addAbsenceYear(Request $request)
    {
        try {
            DB::beginTransaction();
            $params = (object) $request->params;
            $absenceYear = AbsenceYear::find($params->id);
            if (!$absenceYear) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }
            $note = "[HCNS] - Cập nhật tồn phép năm cho nhân sự: " . $params->user;
            AbsenceYear::updateAbsenceYear($absenceYear->user_id, $params->absence_year_val, $note); // hàm update này đã có log trong rồi

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $absenceYear);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceYearController addAbsenceYear: ' . $e->getMessage());
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function syncStaff(Request $request)
    {
        try {
            DB::beginTransaction();
            $officialUser = UserWorking::active()->notAdmin()->Official()->get();
            foreach ($officialUser as $user) {
                $check = AbsenceYear::where('user_id', $user->user_id)->first();
                if (!$check) {
                    $absenceYear = new AbsenceYear();
                    $absenceYear->user_id = $user->user_id;
                    $absenceYear->absence_year_val = 0;
                    $absenceYear->save();

                    # ghi lại log lần update nghỉ phép
                    $note = "[Vietec] - Bắt đầu";
                    LogAbsenceYear::writeLogAbsenceYear($absenceYear->user_id, $note);
                }
            }
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), true);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceYearController syncStaff: ' . $e->getMessage());
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function indexMobile(Request $request)
    {
        try {
            $data = [];
            $departmentId = $request->department_id ?? null;
            $rank_code = $request->rank_code ?? null;
            $keyword = $request->keyword ?? null;

            $model = AbsenceYear::with([
                'user:id,name,staff_code',
                'user.user.department:id,code',
                'user.user.position:id,code',
                'logAbsenceYear', 'user.endOfWorking'
            ])->whereHas('user.endOfWorking', function ($q) {
                $q->active();
            })
                ->when($departmentId, function ($query, $departmentId) {
                    $query->whereHas('user.user.department', function ($subQuery) use ($departmentId) {
                        return $subQuery->where('id',  $departmentId);
                    });
                })
                ->when($rank_code, function ($query, $rank_code) {
                    $query->whereHas('user.user.position', function ($subQuery) use ($rank_code) {
                        return $subQuery->where('code',  $rank_code);
                    });
                })
                ->when($keyword, function ($query, $keyword) {
                    $query->whereHas('user', function ($subQuery) use ($keyword) {
                        return $subQuery->where('name', 'like', '%' . $keyword . '%')
                            ->orWhere('staff_code', 'like', '%' . $keyword . '%');
                    });
                });
            $data = $model->paginate(20);
         
            $rs = (new APIJsonResponse)->responseSuccess((new AbsenceYearTransformer)->transforms($data));
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function viewHistoryMobile(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $month = $request->month;
            $model = LogAbsenceYear::month($month)->whereUserId($user_id)->orderby('updated_at','DESC')->get();
        
            $rs = (new APIJsonResponse)->responseSuccess((new AbsenceYearTransformer)->transforms($model, 1));
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function addAbsenceYearMobile(Request $request)
    {
        try {
            DB::beginTransaction();
            $params = (object) $request->params;
            $absenceYear = AbsenceYear::find($params->id);
            if (!$absenceYear) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }
            $note = "[HCNS] - Cập nhật tồn phép năm cho nhân sự: " . $params->user;
            AbsenceYear::updateAbsenceYear($absenceYear->user_id, $params->absence_year_val, $note);

            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess($absenceYear);
        } catch (\Exception $e) {
            DB::rollback();
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function syncStaffMobile(Request $request)
    {
        try {
            DB::beginTransaction();
            $officialUser = UserWorking::active()->notAdmin()->Official()->get();
            foreach ($officialUser as $user) {
                $check = AbsenceYear::where('user_id', $user->user_id)->first();
                if (!$check) {
                    $absenceYear = new AbsenceYear();
                    $absenceYear->user_id = $user->user_id;
                    $absenceYear->absence_year_val = 0;
                    $absenceYear->save();

                    # ghi lại log lần update nghỉ phép
                    $note = "[Vietec] - Bắt đầu";
                    LogAbsenceYear::writeLogAbsenceYear($absenceYear->user_id, $note);
                }
            }
            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            DB::rollback();
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }
}
