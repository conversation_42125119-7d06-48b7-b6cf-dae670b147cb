<?php

namespace App\Http\Controllers;

use App\Models\BusinessArea;
use App\Models\BusinessAreaDetail;
use App\Models\ProvinceBusinessMarket;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BusinessAreaController extends Controller
{
    public function index()
    {
        try {
            $rs = BusinessArea::with('businessAreaDetails.provinceBusinessMarket')->orderBy('name')->get();
            return response()->json($rs);
        } catch (Exception $e) {
            Log::error("BusinessAreaController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getAll($id)
    {
        try {
            $rs = BusinessArea::withCount([
                'teamSaleAreas' => function ($query) use ($id) {
                    $query->where('position_id', $id);
                },
            ])->orderBy('name')->get();
            return response()->json($rs);
        } catch (Exception $e) {
            Log::error("BusinessAreaController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'id' => 'required|max:50',
            'name' => 'required|max:500',
            'provinces' => 'required|array'
        ], [], [
            'name' => 'Tên khu vực',
            'provinces' => 'Thị trường'
        ]);
        try {
            DB::beginTransaction();
            $model = BusinessArea::find($request->id);
            if ($model) {
                return response()->json(['message' => 'Mã khu vực đã tồn tại'], 400);
            }
            $model = new BusinessArea();
            $model->id = $request->id;
            $model->name = $request->name;
            $model->save();

            // save area
            $provinces = ProvinceBusinessMarket::whereIn('province_id', $request->provinces)->get();
            if (count($provinces) != count($request->provinces)) {
                return response()->json(['message' => 'Thị trường không hợp lệ'], 400);
            }

            $dataInsert = [];
            foreach ($provinces as $p) {
                $dataInsert[] = [
                    'id' => Str::uuid()->toString(),
                    'business_area_id' => $model->id,
                    'province_id' => $p->province_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
            }
            BusinessAreaDetail::insert($dataInsert);

            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("BusinessAreaController store: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function edit(Request $request)
    {
        $request->validate([
            'name' => 'required|max:500',
            'provinces' => 'required|array'
        ], [], [
            'name' => 'Tên khu vực',
            'provinces' => 'Thị trường'
        ]);
        try {
            DB::beginTransaction();
            $model = BusinessArea::find($request->id);
            if (!$model) {
                return response()->json(['message' => 'Không tìm thấy thông tin'], 404);
            }

            $model->name = $request->name;
            $model->save();

            // save area
            $provinces = ProvinceBusinessMarket::whereIn('province_id', $request->provinces)->get();
            if (count($provinces) != count($request->provinces)) {
                return response()->json(['message' => 'Thị trường không hợp lệ'], 400);
            }

            // remove all old data
            BusinessAreaDetail::where('business_area_id', $model->id)->delete();

            $dataInsert = [];
            foreach ($provinces as $p) {
                $dataInsert[] = [
                    'id' => Str::uuid()->toString(),
                    'business_area_id' => $model->id,
                    'province_id' => $p->province_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
            }
            BusinessAreaDetail::insert($dataInsert);

            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("BusinessAreaController edit: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
