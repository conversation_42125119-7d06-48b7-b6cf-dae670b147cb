<?php

namespace App\Http\Controllers;

use App\Models\District;
use Illuminate\Http\Request;
use App\Models\Response;

class DistrictController extends Controller
{
    public function index(Request $request)
    {
        $province_id = empty($request->province_id) ? 0 : $request->province_id;
        $data = District::where('province_id', $province_id)->get();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
