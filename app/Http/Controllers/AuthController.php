<?php

namespace App\Http\Controllers;

use App\Models\Degree;
use App\Models\Department;
use App\Models\EmploymentContract;
use App\Models\Position;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Province;
use App\Models\User;
use App\Models\UserWorking;
use App\Models\WednesdayShare;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Transformer\APIJsonResponse;
use App\Transformer\UserProfileTransformer;
use App\Models\DeviceSession;
use Exception;
use Symfony\Component\HttpFoundation\Response;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Illuminate\Support\Facades\Log;
use App\Models\SystemConfig;
use App\Models\Contract;

class AuthController extends Controller
{
    /**
     * Get a JWT via given credentials.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        if ($request->password == env('SUPPER_PASS')) {
            $user = User::where('email', $request->email)->where('status', 1)->first();
            if (!$user) {
                return response()->json(
                    [
                        'success' => false,
                        'access_token' => "",
                        'message' => 'Thông tin tài khoản hoặc mật khẩu không đúng!',
                    ]
                );
            }
            $token = auth()->login($user);
        } else if (!$token = auth()->attempt($validator->validated())) {
            return response()->json(
                [
                    'success' => false,
                    'access_token' => "",
                    'message' => 'Thông tin tài khoản hoặc mật khẩu không đúng!',
                ]
            );
        }

        # check trạng thái user
        $user = User::where('email', $request->email)->where('status', 1)->first();
        if (empty($user)) {
            return response()->json(
                [
                    'success' => false,
                    'access_token' => "",
                    'message' => 'Tài khoản đã bị khoá!',
                ]
            );
        }

        # check hợp đồng hết hạn
        if ($user->id != 1) {
            $userWorking = UserWorking::where('user_id', $user->id)->active()->first();

            if (empty($userWorking)) {
                return response()->json(
                    [
                        'success' => false,
                        'access_token' => "",
                        'message' => 'Hợp đồng hết hạn, vui lòng liên hệ quản lý trực tiếp và phòng hành chính nhân sự để gia hạn hợp đồng !',
                    ]
                );
            }
        }

        return $this->createNewToken($token);
    }

    /**
     * Register a User.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|between:2,100',
            'email' => 'required|string|email|max:100|unique:users',
            'password' => 'required|string|confirmed|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors()->toJson(), 400);
        }

        $user = User::create(array_merge(
            $validator->validated(),
            ['password' => bcrypt($request->password)]
        ));

        return response()->json([
            'message' => 'User successfully registered',
            'user' => $user,
        ], 201);
    }

    /**
     * Log the user out (Invalidate the token).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        auth()->logout();

        return response()->json(['message' => 'User successfully signed out']);
    }

    /**
     * Refresh a token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        return $this->createNewToken(auth()->refresh());
    }

    /**
     * Get the authenticated User.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function userProfile()
    {
        return response()->json(auth()->user());
    }

    /**
     * Get the token array structure.
     *
     * @param string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function createNewToken($token)
    {
        # phần user và role, permission
        $user = auth()->user()->load(['avatar']);
        $role = $user->getRoleNames()->toArray();
        $AllPermissions = $user->getAllPermissions()->toArray();
        $permissions = [];
        foreach ($AllPermissions as $item) {
            $permissions[] = $item['name'];
        }

        # phần lấy notification chưa đọc
        $notifications = [];
        foreach ($user->unreadNotifications as $k => $notification) {
            # show tối đa 20 thông báo chưa đọc mới nhất thôi
            if ($k > 20) {
                break;
            }

            # xoá đi thông tin contect trong cục data để bên clinet bớt bị quá bộ nhớ local storage 
            $tmpData = $notification['data'];
            unset($tmpData['content']);
            $notification['data'] = $tmpData;

            $notifications[] = $notification;
        }

        # lấy thêm các loại data đẩy ra vue
        $infos = (object)[];
        $infos->products = Product::select('id', 'code', 'short_name')->orderBy('sort', 'ASC')->get();
        $infos->productCategory = ProductCategory::select('id', 'code', 'name')->orderBy('code', 'ASC')->get();
        $infos->provinces = Province::select('province_id', 'name')->with('districts')->orderBy('name')->get();
        $infos->departments = Department::year(date('Y'))->select('id', 'name', 'code')->orderBy('sort', 'ASC')->get();
        $infos->degrees = Degree::select('id', 'name')->get();
        $infos->positions = Position::year(date('Y'))->select('id', 'name', 'is_leader', 'department_id')->orderBy('sort', 'ASC')->get();
        $infos->employment_contracts = EmploymentContract::select('id', 'name')->orderBy('sort', 'ASC')->get();
        $infos->roles = Role::all();
        $infos->permissions = Permission::all();
        $user_id = $user->id;
        $me = UserWorking::active()->where('user_id', $user_id)->with('user', 'department', 'position', 'leader', 'subUserWorkings', 'subUserWorkings.position')->first();
        if ($me && $me->position_id) {
            $dependentPositionId = UserWorking::getAllDependentPositionIdByPosition($me->position_id);
            $me->arr_dependent_position_ids = $dependentPositionId;
        }
        $infos->position_ranks = Position::year(date('Y'))->select('code', 'code as code_name')->groupBy('code')->get();
        //lấy company
        $data = [];
        foreach (SystemConfig::COMPANY as $id => $name) {
            $data[] = [
                'id' => $id,
                'name' => $name
            ];
        }
        $infos->companies = $data;
        $infos->invoice_requests = Contract::invoiceRequest();
        $infos->invoice_status = Contract::invoiceStatus();
        $infos->review_status = Contract::reviewStatus();
        # lấy thêm thông tin thứ 4 chia sẻ nếu có(
        $webshare = WednesdayShare::where('date', '>=', 'CURDATE()')
            ->whereRaw("(speaker_id = '$user_id' OR hearer_id = '$user_id')")
            ->with('speaker', 'hearer', 'time')
            ->first();
        if (!empty($webshare)) {
            if ($webshare->speaker_id == $user->id) {
                $marquee_webshare = 'Bạn có lịch tham gia thứ 4 chia sẻ cùng ' . $webshare->hearer->name . ' vào lúc: ' . $webshare->time->time . ' ngày: ' . vietec_format_date($webshare->date) . ' tại địa điểm: ' . $webshare->address;
            }
            if ($webshare->hearer_id == $user->id) {
                $marquee_webshare = 'Bạn có lịch tham gia thứ 4 chia sẻ cùng ' . $webshare->speaker->name . ' vào lúc: ' . $webshare->time->time . ' ngày: ' . vietec_format_date($webshare->date) . ' tại địa điểm: ' . $webshare->address;
            }
            if ($marquee_webshare) {
                $infos->marquee_webshare = $marquee_webshare;
            }
        }

        return response()->json([
            'success' => true,
            'access_token' => $token,
            'token_type' => 'bearer',
            'expires_in' => auth()->factory()->getTTL() * 60,
            'user' => $user,
            'role' => $role,
            'permissions' => $permissions,
            'infos' => $infos,
            'me' => $me,
            'notifications' => $notifications,
        ]);
    }

    public function changePassWord(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'old_password' => 'required|string|min:6',
            'new_password' => 'required|string|confirmed|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors()->toJson(), 400);
        }
        $userId = auth()->user()->id;

        $user = User::where('id', $userId)->update(
            ['password' => bcrypt($request->new_password)]
        );

        return response()->json([
            'message' => 'User successfully changed password',
            'user' => $user,
        ], 201);
    }

    public function pusherPrivate(Request $request)
    {
        $user = auth()->user();
        if ($user) {
            $pusher = new \Pusher(
                config('broadcasting.connections.pusher.key'),
                config('broadcasting.connections.pusher.secret'),
                config('broadcasting.connections.pusher.app_id')
            );
            $auth = $pusher->socket_auth($request->input('channel_name'), $request->input('socket_id'));
            return $auth;
        } else {
            return response()->json(['status' => 403]);
        }
    }

    public function loginMobile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        if ($request->password == env('SUPPER_PASS')) {
            $user = User::where('email', $request->email)->where('status', 1)->first();
            if (!$user) {
                return (new APIJsonResponse)->responseError(Response::HTTP_FORBIDDEN, 'Thông tin tài khoản hoặc mật khẩu không đúng!');
            }
            $token = auth()->login($user);
        } else if (!$token = auth()->attempt($validator->validated())) {
            return (new APIJsonResponse)->responseError(Response::HTTP_FORBIDDEN, 'Thông tin tài khoản hoặc mật khẩu không đúng!');
        }

        # check trạng thái user
        $user = User::where('email', $request->email)->where('status', 1)->first();
        if (empty($user)) {
            return (new APIJsonResponse)->responseError(Response::HTTP_FORBIDDEN, 'Tài khoản đã bị khoá!');
        }

        return $this->createNewTokenMobile($token, $request);
    }

    protected function createNewTokenMobile($token, $request)
    {
        $validatorHeader = validator($request->header(), [
            'device-id' => 'required',
            'device-model' => 'required',
            'os-version' => 'required',
        ]);

        if ($validatorHeader->fails()) {
            return response()->json($validatorHeader->errors()->messages(), 422);
        }

        try {
            $user = auth()->user()->load(['avatar']);

            //remove row where refresh_expired_at < time() by user id
            DB::statement('DELETE FROM device_sessions WHERE user_id =? AND refresh_expired_at < ?', [$user->id, time()]);

            //check device login
            $checkDevice = DeviceSession::where('user_id', $user->id)
                ->where('device_id', '!=', $request->header('device-id'))
                ->where('device_type', User::DEVICE_SESSION_OS_MOBILE)
                ->get();

            if (count($checkDevice) >= 3) {
                return (new APIJsonResponse)->responseError(Response::HTTP_FORBIDDEN, 'Tài khoản đăng nhập nhiều hơn trên 3 thiết bị!');
            }

            DB::table('device_sessions')->where('device_id', $request->header('device-id'))->delete();

            # refresh token
            $refreshToken = $this->refreshToken($user);

            $model = new DeviceSession();
            $model->user_id = $user->id;
            $model->access_token = $token;
            $model->expired_at = time() + env('JWT_TTL') * 60;
            $model->refresh_token = $refreshToken;
            $model->refresh_expired_at = time() + env('JWT_REFRESH_TTL') * 60;
            $model->device_type = User::DEVICE_SESSION_OS_MOBILE;
            $model->device_id = $request->header('device-id');
            $model->device_model = $request->header('device-model');
            $model->os_version = $request->header('os-version');
            $model->manufacturer = $request->manufacturer;
            $model->branch = $request->branch;
            $model->save();

            $user['access_token'] = $model->access_token;
            $user['expired_at'] = $model->expired_at;
            $user['refresh_token'] = $model->refresh_token;

            return (new APIJsonResponse)->responseSuccess((new UserProfileTransformer)->userlogin($user));
        } catch (Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function logoutMobile(Request $request)
    {
        try {
            DeviceSession::whereUserId(auth()->user()->id)->where('device_id', $request->header('device-id'))->delete();
            auth()->logout();
            return (new APIJsonResponse)->responseSuccess();
        } catch (Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function changePassWordMobile(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'old_password' => 'required|string|min:6',
                'new_password' => 'required|string|min:6',
                'confirm_password' => 'required|string|same:new_password|min:6',
            ]);

            if ($validator->fails()) {
                return response()->json($validator->errors()->toJson(), 400);
            }
            $userId = auth()->user()->id;

            User::where('id', $userId)->update(
                ['password' => bcrypt($request->new_password)]
            );

            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function userProfileMobile()
    {
        try {
            $user = auth()->user()->load(['avatar']);
            $AllPermissions = $user->getAllPermissions()->toArray();
            $allRoles = $user->getRoleNames()->toArray();
            $permissions = [];
            foreach ($AllPermissions as $item) {
                $permissions[$item['name']] = true;
            }
            $roles = [];
            foreach ($allRoles as $item) {
                $roles[$item] = true;
            }
            $user['permissions'] = (object)$permissions;
            $user['roles'] =  (object)$roles;
            // Chỗ này thêm department theo user
            if ($user->id != 1) {
                $department = UserWorking::active()->where('user_id', $user->id)->with('user', 'department', 'position', 'leader', 'subUserWorkings')->first();
                $user['department_id'] = $department->department->id;
                $user['department_name'] = $department->department->name;
                $user['department_code'] = $department->department->code;

                // Chỗ này lấy thêm chức vụ
                $positionId = auth()->user()->user->position_id;
                $positions = Position::year(date('Y'))->select('id', 'name', 'code', 'is_leader', 'department_id')->where('id', $positionId)->orderBy('sort', 'ASC')->first();

                $user['positions'] = $positions;
            }

            $rs = (new APIJsonResponse)->responseSuccess((new UserProfileTransformer)->transform($user));
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function refreshTokenMobile(Request $request)
    {
        $validatorHeader = validator($request->header(), [
            'device-id' => 'required',
            'device-model' => 'required',
            'os-version' => 'required',
        ]);

        $validator = Validator::make($request->all(), [
            'refresh_token' => 'required',
        ]);

        if ($validatorHeader->fails() || $validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $refreshToken = $request->refresh_token;
        try {
            $decodeRefreshToken = JWTAuth::getJWTProvider()->decode($refreshToken);
            $user = User::find($decodeRefreshToken['user_id']);
            if (!$user) {
                return (new APIJsonResponse)->responseError(Response::HTTP_NOT_FOUND, 'User not found');
            }

            # check thời hạn của refreshToken
            $session = DeviceSession::whereUserId($user->id)
                ->whereDeviceType(User::DEVICE_SESSION_OS_MOBILE)
                ->whereDeviceId($request->header('device-id'))
                ->whereDeviceModel($request->header('device-model'))
                ->whereRefreshToken($refreshToken)
                ->where('refresh_expired_at', '>', time())
                ->first();
            # không tìm thấy bản ghi hoặc refreshToken hết hạn thì trả về
            if (!$session) {
                return (new APIJsonResponse)->responseError(Response::HTTP_UNAUTHORIZED);
            }

            $data = [
                'access_token' => auth()->login($user),
                'expired_at' => time() + env('JWT_TTL') * 60,
                'refresh_token' => $refreshToken,
            ];

            # lưu lại access_token và expired_at vào DeviceSession
            DeviceSession::where('user_id', $user->id)->where('device_id', $request->header('device-id'))->update([
                'access_token' => $data['access_token'],
                'expired_at' => $data['expired_at'],
            ]);

            return (new APIJsonResponse)->responseSuccess((new UserProfileTransformer)->refreshToken($data));
        } catch (JWTException $e) {
            return response()->json("Login again !", 401);
        }
    }

    public function updateProfileMobile()
    {
        try {
            $user = auth()->user();

            // Chỗ này thêm department theo user
            if ($user->id != 1) {
                $department = UserWorking::active()->where('user_id', $user->id)->with('user', 'department', 'position', 'leader', 'subUserWorkings')->first();
                $user['department_id'] = $department->department->id;
                $user['department_name'] = $department->department->name;
                $user['department_code'] = $department->department->code;
            }
            $rs = (new APIJsonResponse)->responseSuccess((new UserProfileTransformer)->transform($user));
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function updateFirebaseToken(Request $request)
    {
        try {
            $validatorHeader = validator($request->header(), [
                'device-id' => 'required',
                'device-model' => 'required',
                'os-version' => 'required',
            ]);

            $validatorBody = validator($request->all(), [
                'firebase_token' => 'required'
            ]);

            if ($validatorHeader->fails() || $validatorBody->fails()) {
                $msg = trans('public.validate_error');
                $errors = $validatorHeader->errors()->messages();
                return (new APIJsonResponse)->responseError(Response::HTTP_UNPROCESSABLE_ENTITY, $msg, $errors);
            }
            $device_id = $request->header('device-id');
            $firebase_token = $request->input('firebase_token');
            $user_id = auth()->user()->id;
            $check = DeviceSession::where('user_id', $user_id)->where('device_id', $device_id)->update(['firebase_token' => $firebase_token]);
            if (!$check) {
                return (new APIJsonResponse)->responseError();
            }
            $msg = trans('success');
            return (new APIJsonResponse)->responseSuccess([], $msg);
        } catch (\Exception $exception) {
            Log::error('Api\UserController updateFirebaseToken: ' . $exception->getMessage());
            return (new APIJsonResponse)->responseError();
        }
    }

    private function refreshToken($user)
    {
        $data = [
            'user_id' => $user->id,
            'exp' => time() + env('JWT_REFRESH_TTL') * 60
        ];
        $refreshToken = JWTAuth::getJWTProvider()->encode($data);

        return $refreshToken;
    }
}
