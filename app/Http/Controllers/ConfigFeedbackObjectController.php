<?php

namespace App\Http\Controllers;

use App\Models\ConfigFeedbackObject;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ConfigFeedbackObjectController extends Controller
{
    public function index(Request $request)
    {
        try {
            $rs = ConfigFeedbackObject::orderBy('name')->get();
            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('ConfigFeedbackObjectController index: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra.'], 500);
        }
    }

    public function store(Request $request)
    {
        $rules = $request->id
            ? [
                'name' => ['required', 'max:50', 'unique:config_feedback_object,name,' . $request->id],
                'code' => 'required|max:50'
            ]
            : [
                'name' => ['required', 'max:50', 'unique:config_feedback_object'],
                'code' => 'required|max:50'
            ];

        $request->validate($rules, [], ['name' => 'Tên đối tượng']);

        try {
            $model = new ConfigFeedbackObject();
            if ($request->id) {
                $model = ConfigFeedbackObject::find($request->id);

                if (!$model) {
                    return response()->json(['message' => 'Dữ liệu không tồn tại.'], 404);
                }
            }
            $model->name = $request->name;
            $model->code = $request->code;
            $model->save();
            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (Exception $e) {
            Log::error('ConfigFeedbackObjectController store: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra.'], 500);
        }
    }

    public function delete(Request $request)
    {
        try {
            $model = ConfigFeedbackObject::find($request->id);

            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại.'], 404);
            }

            $model->delete();
            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (Exception $e) {
            Log::error('ConfigFeedbackObjectController delete: ' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi xảy ra.'], 500);
        }
    }
}
