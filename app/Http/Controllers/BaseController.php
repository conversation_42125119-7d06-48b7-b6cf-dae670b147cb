<?php

namespace App\Http\Controllers;

use App\Http\Requests\ClientStoreRequest;
use App\Http\Requests\ClientUpdateRequest;
use App\Models\Ticket;
use App\Models\Province;
use App\Models\District;
use App\Models\Ward;
use App\Transformer\TicketTransformer;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use League\Fractal\Pagination\IlluminatePaginatorAdapter;
use League\Fractal\Resource\Collection;
use League\Fractal\Manager;

class BaseController extends Controller
{
    public function __construct()
    {
        
    }

    /**
     * Trả về dữ liệu đã qua Transformer
     */
    public function response_paginator($paginator, $transformer)
    {
        if ($paginator != null && $transformer != null) {
            
            $items = $paginator->getCollection();

            $resource = new Collection($items, new $transformer);
            $resource->setPaginator(new IlluminatePaginatorAdapter($paginator));

            $fractal = new Manager();
            return ['data' => $fractal->createData($resource)->toArray()];
        } else {
            return ['data' => ''];
        }
    }
}
