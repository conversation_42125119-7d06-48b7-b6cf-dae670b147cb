<?php

namespace App\Http\Controllers;

use App\Models\EquipmentType;
use App\Models\Response;
use Illuminate\Http\Request;

class EquipmentTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = EquipmentType::with('supplier', 'equipments.importWarehouseHistory')
            ->withSum('exWarehouseHistory', 'amount')
            ->withSum('equipments', 'amount')
            ->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'code' => 'required|max:255',
            'unit' => 'required',
        ], [], [
            'name' => 'Tên loại hàng hóa',
            'code' => 'Mã loại hàng hóa',
            'unit' => 'Yêu cầu nhập đơn vị',
        ]);
        try {
            $params = $request->all();
            EquipmentType::create($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\EquipmentType $EquipmentType
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'code' => 'required|max:255',
            'unit' => 'required',
        ], [], [
            'name' => 'Tên loại hàng hóa',
            'code' => 'Mã loại hàng hóa',
            'unit' => 'Yêu cầu nhập đơn vị',
        ]);
        try {
            $params = $request->all();
            unset($params['supplier']);
            EquipmentType::where('id', $request->id)->update($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EquipmentType $EquipmentType
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = EquipmentType::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function get_all_equipment()
    {
        $data = EquipmentType::all();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
