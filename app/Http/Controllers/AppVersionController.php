<?php

namespace App\Http\Controllers;

use App\Models\AppVersion;
use App\Transformer\APIJsonResponse;

class AppVersionController extends Controller
{
    public function getAppVersion()
    {
        try {
            $android = AppVersion::select('values', 'type')->where('type', 'android')->first();
            $ios = AppVersion::select('values', 'type')->where('type', 'ios')->first();
            $maintenance = AppVersion::select('values', 'type')->where('type', 'maintenance')->first();
            return (new APIJsonResponse())->responseSuccess([
                'android' => json_decode($android->values),
                'ios' => json_decode($ios->values),
                'maintenance' => json_decode($maintenance->values)
            ]);
        } catch (\Exception $e) {
            return (new APIJsonResponse())->responseError();
        }
    }
}
