<?php

namespace App\Http\Controllers;

use App\Builder\NotificationBuilder;
use App\Builder\NotificationConfig;
use App\Events\BusinessPlaneEvent;
use App\Http\Requests\BusinessPlanRequest;
use App\Models\AdditionalBusinessCost;
use App\Models\BusinessPartner;
use App\Models\BusinessPlan;
use App\Models\BusinessPlanDetail;
use App\Models\BusinessReport;
use App\Models\BusinessReportDetail;
use App\Models\User;
use App\Models\UserWorking;
use App\Notifications\BusinessPlanNotification;
use App\Services\FirebaseNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Response;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\IOFactory;

class BusinessPlanController extends Controller
{
    public function list(Request $request)
    {
        $date = explode('-', $request->month);
        $month = $date[0];
        $year = $date[1];
        $query = BusinessPlan::whereMonth('to_date', $month)->whereYear('to_date', $year)
            ->when($request->department_id, function ($q, $departmentId) {
                $q->whereHas('created_by.endOfWorking', function ($subQuery) use ($departmentId) {
                    return $subQuery->where('department_id', $departmentId);
                });
            })->when($request->status, function ($q, $status) {
                $q->where('status', $status);
            });

        $dt = $query->with('created_by.endOfWorking', 'confirm_by')->orderBy('from_date', 'DESC')->paginate(20);
        $status = BusinessPlan::convertStatus();
        $data = [
            'data' => $dt,
            'status' => $status
        ];
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function show($id)
    {
        $data = BusinessPlan::where('id', $id)->with('plans', 'additional_costs', 'partners')->first();
        $isShowBtnApprove = BusinessPlan::showBtnApprove($data);
        $isShowBtnSave = BusinessPlan::showBtnSave($data);
        return (object)['data_info' => $data, 'isShowBtnApprove' => $isShowBtnApprove, 'isShowBtnSave' => $isShowBtnSave];
    }

    public function create(BusinessPlanRequest $request)
    {
        try {
            $data = (object)$request->data;
            $staffs = [];
            foreach ($data->staffs as $e) {
                $staff = (object)$e;
                $staffs[] = [
                    'id' => $staff->id,
                    'name_code' => $staff->name_code
                ];
            }

            DB::beginTransaction();
            //store Business Plan
            $business_plan = new BusinessPlan();
            $business_plan->from_date = $data->from_date;
            $business_plan->to_date = $data->to_date;
            $business_plan->expense = $data->expense;
            $business_plan->stay = $data->stay;
            $business_plan->other_costs = $data->other_costs;
            $business_plan->vehicle = json_encode($data->vehicle);
            $business_plan->staffs = json_encode($staffs);
            $business_plan->created_by = auth()->user()->id;
            $business_plan->reason = $data->reason;
            $business_plan->exception = $data->exception;
            $business_plan->save();
            BusinessPartner::store($data, $business_plan->id);
            BusinessPlanDetail::store($data, $business_plan->id);
            AdditionalBusinessCost::store($data, $business_plan->id, BusinessPlan::PLAN_TYPE);
            //send email thông báo cho leader
            $leader = BusinessPlan::getLeader(auth()->user()->id);
            $config = new NotificationConfig();
            $config->setUser($leader);
            $contentMail = (object)[];
            $contentMail->subject = "[VIETEC-QLDN]- Thông báo duyệt đơn đăng ký công tác cho nhân sự : " . auth()->user()->name;
            $contentMail->receiver = env('APP_ENV') == 'production' ? $leader->email : '<EMAIL>';
            $contentMail->receiver_name = $leader->name;
            $contentMail->message = "Anh/chị vui lòng duyệt đơn đăng ký công tác của nhân sự " . auth()->user()->name . ' Từ ngày ' . Carbon::parse($data->from_date)->format('d-m-Y') . ' Đến ngày ' . Carbon::parse($data->to_date)->format('d-m-Y');
            $config->setContentMails([$contentMail]);
            NotificationBuilder::getBuilder()->sendMessage($config, [NotificationBuilder::EMAIL]);
            DB::commit();
            return response()->json(['message' => 'Thông báo được gửi đến Lãnh đạo trực tiếp của bạn!']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function edit(BusinessPlanRequest $request, $id)
    {
        try {
            $data = (object)$request->data;
            $staffs = [];
            foreach ($data->staffs as $e) {
                $staff = (object)$e;
                $staffs[] = [
                    'id' => $staff->id,
                    'name_code' => $staff->name_code
                ];
            }

            if (auth()->user()->id != $data->created_by || $data->status != BusinessPlan::WAITING) {
                return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
            }
            DB::beginTransaction();
            BusinessPlan::where('id', $id)->update([
                'from_date' => $data->from_date,
                'to_date' => $data->to_date,
                'expense' => $data->expense,
                'stay' => $data->stay,
                'other_costs' => $data->other_costs,
                'vehicle' => json_encode($data->vehicle),
                'staffs' => json_encode($staffs),
                'reason' => $data->reason,
                'exception' => $data->exception
            ]);
            BusinessPartner::where('business_plan_id', $id)->delete();
            BusinessPartner::store($data, $id);
            BusinessPlanDetail::where('business_plan_id', $id)->delete();
            BusinessPlanDetail::store($data, $id);
            AdditionalBusinessCost::where('business_plan_id', $id)->delete();
            AdditionalBusinessCost::store($data, $id, BusinessPlan::PLAN_TYPE);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController edit: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function delete($id)
    {
        try {
            DB::beginTransaction();
            $query = BusinessPlan::where('id', $id);
            $dt = $query->first();

            if ($dt->status == BusinessPlan::WAITING && auth()->user()->id == $dt->created_by) {
                $query->delete();
            } else {
                return response()->json(['message' => 'Bạn không có quyền xoá bản ghi này!'], 403);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController delete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function approve($id)
    {
        try {
            DB::beginTransaction();
            $plan = BusinessPlan::where('id', $id)->first();
            $leader = BusinessPlan::getLeader($plan->created_by);
            if ($leader->id != auth()->user()->id || $plan->status != BusinessPlan::WAITING) {
                return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
            }
            BusinessPlan::where('id', $id)->update(['confirm_by' => auth()->user()->id, 'status' => BusinessPlan::PLAN_APPROVED]);
            $user = User::select('id', 'name')->where('id', $plan->created_by)->first();
            $total_costs = array_sum([$plan->expense, $plan->stay, $plan->other_costs]);
            if ($total_costs > 0) {
                $config = new NotificationConfig();
                $config->setUser($user);
                $contentMail = (object)[];
                $contentMail->subject = "[VIETEC-QLDN]- Thông báo đề xuất tạm ứng chi phí công tác";
                $contentMail->receiver = env('APP_ENV') == 'production' ? '<EMAIL>' : '<EMAIL>';
                $contentMail->cc = '<EMAIL>';
                $contentMail->receiver_name = "Phòng Kế toán";
                $contentMail->message = "Hiện tại nhân sự " . $user->name . " vừa gửi đề xuất tạm ứng chi phí công tác. Bộ phận Kế toán vui lòng kiểm tra lại thông tin và tiến hành xử lý theo quy trình.";
                $config->setContentMails([$contentMail]);
                NotificationBuilder::getBuilder()->sendMessage($config, [NotificationBuilder::EMAIL]);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController approve: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function reject($id)
    {
        try {
            DB::beginTransaction();
            $plan = BusinessPlan::where('id', $id)->first();
            $leader = BusinessPlan::getLeader($plan->created_by);
            if ($leader->id != auth()->user()->id || $plan->status != BusinessPlan::WAITING) {
                return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
            }
            BusinessPlan::where('id', $id)->update(['confirm_by' => auth()->user()->id, 'status' => BusinessPlan::PLAN_REJECTED]);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exportRequestAdvance($id)
    {
        $plan = BusinessPlan::where('id', $id)->with('additional_costs')->first();
        if ($plan->status != BusinessPlan::PLAN_APPROVED || $plan->created_by != auth()->user()->id) {
            return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
        }
        $inputFileName = 'templates/DON_TAM_UNG.xlsx';
        $fileType = IOFactory::identify($inputFileName);
        $objReader = IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);
        $row = 17;
        $sheet->setCellValue('A6', 'Ngày ' . date("d") . ' tháng ' . date("m") . ' năm ' . date("Y"))->getStyle('C6')->getAlignment()->setHorizontal('center');
        $sheet->setCellValue('A9', 'Họ và tên: ' . auth()->user()->name);
        $sheet->setCellValue('A10', 'Bộ phận: ' . auth()->user()->user->department->name);
        $sheet->setCellValue('A11', 'Lý do tạm ứng: ' . $plan->reason . '. Đề nghị tạm ứng công tác Từ ngày ' . Carbon::parse($plan->from_date)->format('d-m-Y') . ' Đến ngày ' . Carbon::parse($plan->to_date)->format('d-m-Y'));
        $sheet->setCellValue('D14', $plan->expense);
        $sheet->setCellValue('D15', $plan->stay);
        $sheet->setCellValue('D16', $plan->other_costs);
        foreach ($plan->additional_costs as $e) {
            $sheet->setCellValue('C' . $row, $e->content);
            $sheet->setCellValue('D' . $row, $e->amount);
            $row++;
        }
        $total_row = 3 + count($plan->additional_costs);
        $sheet->setCellValue('C27', 'Tổng cộng(' . $total_row . ')')->getStyle('C27')->getFont()->setBold(true);
        $sheet->setCellValue('A41', auth()->user()->name);
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="DON_TAM_UNG.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (\Exception $exception) {
            throw $exception;
        }
    }

    public function getVehicle()
    {
        $vehicles = BusinessPlan::convertVehicle();
        return $vehicles;
    }

    public function getStaffAndVehiclesApi(Request $request)
    {
        $vehicles = $this->getVehicle();
        $staffs = User::select('id', 'name', 'staff_code')->where('id', '<>', '1')->whereHas('endOfWorking', function ($query) {
            $query->active();
        })->get();
        return \response()->json(['vehicles' => $vehicles, 'staffs' => $staffs]);
    }

    public function listBusinessPlanApi(Request $request)
    {
        try {
            $condition = [];
            if (isset($request->status)) {
                $condition['status'] = $request->status;
            }
            $fromDate = $request->from_date;

            $data = BusinessPlan::with(['created_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }, 'confirm_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }])->where($condition)->where(function ($query) use ($fromDate) {
                if ($fromDate) {
                    $formatDate = Carbon::createFromFormat('Y-m-d', $fromDate . '-01');
                    $startOfMonth = $formatDate->startOfMonth()->format('Y-m-d');
                    $endOfMonth = $formatDate->endOfMonth()->format('Y-m-d');
                    $query->where('from_date', '>=', $startOfMonth)->where('to_date', '<=', $endOfMonth)->get();
                } else {
                    $query->get();
                }
            })->where(function ($query) use ($request) {
                $departmentId = $request->department_id;
                if ($departmentId) {
                    $query->whereHas('created_by.endOfWorking', function ($subQuery) use ($departmentId) {
                        return $subQuery->where('department_id', $departmentId);
                    });
                } else {
                    $query->get();
                }
            })->paginate(20);
            return \response()->json(['data' => $data]);
        } catch (\Exception $e) {
            Log::error('BusinessPlanController listBusinessPlanApi: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function deleteBusinessPlan(Request $request)
    {
        try {
            $id = $request->id;
            $businessPlan = BusinessPlan::find($id);
            if (!$businessPlan) {
                return response()->json(['message' => 'Không tìm thấy kế hoạch công tác'], 404);
            }
            // check use is created by
            $user = auth()->user();
            if ($businessPlan->created_by != $user->id) {
                return response()->json(['message' => 'Bạn không có quyền xóa kế hoạch công tác này'], 401);
            }
            // check status
            if ($businessPlan->status != BusinessPlan::WAITING) {
                return response()->json(['message' => 'Kế hoạch công tác đã được xác nhận, Không thể xoá'], 400);
            }
            // delete business report
            DB::beginTransaction();
            BusinessPlanDetail::where('business_plan_id', $id)->delete();
            $businessReports = BusinessReport::where('business_plan_id', $id)->get();
            foreach ($businessReports as $report) {
                BusinessReportDetail::where('business_report_id', $report->id)->delete();
            }
            BusinessReport::where('business_plan_id', $id)->delete();

            $businessPlan->delete();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController deleteBusinessPlan: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function createMobile(Request $request)
    {
        $validate = $request->validate([
            'from_date' => 'required|date_format:d-m-Y',
            'to_date' => 'required|date_format:d-m-Y',
            'expense' => 'required|integer',
            'stay' => 'required|integer',
            'staffs' => 'required',
            'vehicles' => 'required',
            'plans.*.name' => 'required',
            'plans.*.content' => 'required',
            'additional_costs.*.amount' => 'required',
            'additional_costs.*.content' => 'required',
            'partners.0.name' => 'required',
            'partners.0.position' => 'required',
            'partners.0.phone' => 'required',
        ]);
        if (!$validate) {
            return response()->json(['message' => 'Dữ liệu không hợp lệ'], 400);
        }
        try {
            $fromDate = date('Y-m-d', strtotime($request->from_date));
            $toDate = date('Y-m-d', strtotime($request->to_date));
            if ($fromDate > $toDate) {
                return response()->json(['message' => 'Thời gian không hợp lệ'], 400);
            }
            DB::beginTransaction();
            //store Business Plan
            $businessPlan = new BusinessPlan();
            $businessPlan->from_date = $fromDate;
            $businessPlan->to_date = $toDate;
            $businessPlan->expense = $request->expense;
            $businessPlan->stay = $request->stay;
            $businessPlan->vehicle = json_encode($request->vehicles);
            $businessPlan->staffs = json_encode($request->staffs);
            $businessPlan->created_by = auth()->user()->id;
            $businessPlan->exception = $request->exception;
            $businessPlan->other_costs = $request->other_costs;
            $businessPlan->save();
            BusinessPartner::store($request, $businessPlan->id);
            AdditionalBusinessCost::store($request, $businessPlan->id, BusinessPlan::PLAN_TYPE);
            //store business plan detail
            foreach ($request->plans as $p) {
                $convertPlan = (object)$p;
                $businessPlanDetail = new BusinessPlanDetail();
                $businessPlanDetail->business_plan_id = $businessPlan->id;
                $businessPlanDetail->name = $convertPlan->name;
                $businessPlanDetail->content = $convertPlan->content;
                $businessPlanDetail->save();
            }
            DB::commit();

            // push notification to leader
            $leaderPositions = UserWorking::allActive()
                ->where('user_id', auth()->user()->id)->first();
            $user = UserWorking::allActive()
                ->where('position_id', $leaderPositions->position_id)->first();
            $leaderUser = User::find($user->id);
            //
            $config = new NotificationConfig();
            $config->setUser($leaderUser);
            $message = [
                'title' => 'Bạn có một yêu cầu duyệt kế hoạch công tác mới',
                'link' => '/business-plan-show/',
                'content' => 'Duyệt kế hoạch công tác cho nhóm nhân viên ' . auth()->user()->name,
                'option' => 'show'
            ];
            // set config notification mobile
            $config->setNotification(function () use ($message) {
                return new BusinessPlanNotification($message);
            });
            $config->setMsg($message);
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_NOTIFICATION);
            // set config event broadcast web
            $config->setBroadcast(function () use ($leaderUser, $message) {
                return new BusinessPlaneEvent($leaderUser, $message);
            });
            // set config email
            $contentMail = (object)[];
            $contentMail->subject = "[VIETEC-QLDN]- Thông báo duyệt đơn đăng ký công tác cho nhân sự : " . auth()->user()->name;
            $contentMail->receiver = env('APP_ENV') == 'local' ? $leaderUser->email : '<EMAIL>';
            $contentMail->receiver_name = $leaderUser->name;
            $contentMail->message = "Anh/chị vui lòng duyệt đơn đăng ký công tác của nhân sự " . auth()->user()->name . " từ ngày " . $fromDate . " đến ngày " . $toDate;
            $config->setContentMails([$contentMail]);

            NotificationBuilder::getBuilder()->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT, NotificationBuilder::EMAIL]);

            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController createMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function editMobile(Request $request)
    {
        $validate = $request->validate([
            'id' => 'required|integer',
            'from_date' => 'required|date_format:d-m-Y',
            'to_date' => 'required|date_format:d-m-Y',
            'expense' => 'required|integer',
            'stay' => 'required|integer',
            'staffs' => 'required',
            'vehicles' => 'required',
            'plans.*.name' => 'required',
            'plans.*.content' => 'required',
            'additional_costs.*.amount' => 'required',
            'additional_costs.*.content' => 'required',
            'partners.0.name' => 'required',
            'partners.0.position' => 'required',
            'partners.0.phone' => 'required',
        ]);
        if (!$validate) {
            return response()->json(['message' => 'Dữ liệu không hợp lệ'], 400);
        }
        try {
            $fromDate = date('Y-m-d', strtotime($request->from_date));
            $toDate = date('Y-m-d', strtotime($request->to_date));
            if ($fromDate > $toDate) {
                return response()->json(['message' => 'Thời gian không hợp lệ'], 400);
            }
            $businessPlan = BusinessPlan::find($request->id);
            switch ($businessPlan->status) {
                case BusinessPlan::PLAN_APPROVED:
                case BusinessPlan::PLAN_REJECTED:
                case BusinessPlan::REPORT_APPROVED:
                case BusinessPlan::REPORT_REJECTED:
                    return response()->json(['message' => 'Kế hoạch đã được hoàn thành không thể chỉnh sửa'], 400);
                    break;
                case BusinessPlan::WAITING:
                    break;
            }
            $createByUserId = $businessPlan->created_by;
            $currentUser = auth()->user();
            if (!$createByUserId || $createByUserId != $currentUser->id) {
                return response()->json(['message' => 'Bạn không có quyền chỉnh sửa kế hoạch này'], 400);
            }
            // update business plan
            $businessPlan->from_date = $fromDate;
            $businessPlan->to_date = $toDate;
            $businessPlan->expense = $request->expense;
            $businessPlan->stay = $request->stay;
            $businessPlan->vehicle = json_encode($request->vehicles);
            $businessPlan->staffs = json_encode($request->staffs);
            $businessPlan->exception = $request->exception;
            $businessPlan->other_costs = $request->other_costs;
            DB::beginTransaction();
            $businessPlan->save();


            //update business plan detail
            foreach ($request->plans as $p) {
                $convertPlan = (object)$p;
                if (isset($convertPlan->id)) {

                    //check if plan_id is not of this business plan
                    $businessPlanDetail = BusinessPlanDetail::where('id', $convertPlan->id)->where('business_plan_id', $businessPlan->id)->first();
                    if (!isset($businessPlanDetail->id)) {
                        continue;
                    }
                    // update business plane detail
                    $businessPlanDetail->name = $convertPlan->name;
                    $businessPlanDetail->content = $convertPlan->content;
                    $businessPlanDetail->save();
                } else {
                    // create business plane detail
                    $businessPlanDetail = new BusinessPlanDetail();
                    $businessPlanDetail->business_plan_id = $businessPlan->id;
                    $businessPlanDetail->name = $convertPlan->name;
                    $businessPlanDetail->content = $convertPlan->content;
                    $businessPlanDetail->save();
                }
            }

            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController createMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function detailMobile(Request $request)
    {
        try {
            $validation = $request->validate([
                'id' => 'required|integer'
            ]);
            if (!$validation) {
                return response()->json(['message' => 'Dữ liệu không hợp lệ'], 400);
            }
            $user = auth()->user();
            // check permission
            $leaderById = UserWorking::where('user_id', $user->id)->whereHas('position', function ($query) {
                $query->where('is_leader', 1);
            })->first();

            $businessPlan = BusinessPlan::where('id', $request->id)->with(['plans' => function ($query) {
                $query->select('id', 'name', 'content', 'business_plan_id')->get();
            }, 'created_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }, 'confirm_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }])->first();
            if (!$businessPlan) {
                return response()->json(['message' => 'Không tìm thấy kế hoạch'], 404);
            }
            $isSuccess = false;
            switch ($businessPlan->status) {
                case BusinessPlan::PLAN_APPROVED:
                case BusinessPlan::PLAN_REJECTED:
                case BusinessPlan::REPORT_APPROVED:
                case BusinessPlan::REPORT_REJECTED:
                    $isSuccess = true;
                    break;
                case BusinessPlan::WAITING:
                    break;
            }
            $businessPlan['user_can_approve'] = isset($leaderById) && !$isSuccess;
            return response()->json(['data' => $businessPlan]);
        } catch (\Exception $e) {
            Log::error('BusinessPlanController detailMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function approveMobile(Request $request)
    {
        try {
            $user = auth()->user();
            // check permission
            $isLeader = UserWorking::where('user_id', $user->id)->whereHas('position', function ($query) {
                $query->where('is_leader', 1);
            })->first();
            if (!isset($user) || !$isLeader) {
                // return 401
                return response()->json(['message' => 'Bạn không có quyền duyệt kế hoạch'], 401);
            }
            $validation = $request->validate([
                'id' => 'required|integer'
            ]);
            if (!$validation) {
                return response()->json(['message' => 'Dữ liệu không hợp lệ'], 400);
            }
            $businessPlan = BusinessPlan::find($request->id);
            if (!$businessPlan) {
                return response()->json(['message' => 'Không tìm thấy kế hoạch'], 404);
            }
            switch ($businessPlan->status) {
                case BusinessPlan::PLAN_APPROVED:
                case BusinessPlan::PLAN_REJECTED:
                case BusinessPlan::REPORT_APPROVED:
                case BusinessPlan::REPORT_REJECTED:
                    return response()->json(['message' => 'Kế hoạch đã được hoàn thành không thể từ chối'], 400);
                    break;
                case BusinessPlan::WAITING:
                    break;
            }
            DB::beginTransaction();
            BusinessPlan::where('id', $request->id)->update(['confirm_by' => $user->id, 'status' => BusinessPlan::PLAN_APPROVED]);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController approveMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function rejectMobile(Request $request)
    {
        try {
            $user = auth()->user();
            // check permission
            $isLeader = UserWorking::where('user_id', $user->id)->whereHas('position', function ($query) {
                $query->where('is_leader', 1);
            })->first();
            if (!isset($user) || !$isLeader) {
                // return 401
                return response()->json(['message' => 'Bạn không có quyền duyệt kế hoạch'], 401);
            }
            $validation = $request->validate([
                'id' => 'required|integer'
            ]);
            if (!$validation) {
                return response()->json(['message' => 'Dữ liệu không hợp lệ'], 400);
            }
            $businessPlan = BusinessPlan::find($request->id);
            if (!$businessPlan) {
                return response()->json(['message' => 'Không tìm thấy kế hoạch'], 404);
            }
            switch ($businessPlan->status) {
                case BusinessPlan::PLAN_APPROVED:
                case BusinessPlan::PLAN_REJECTED:
                case BusinessPlan::REPORT_APPROVED:
                case BusinessPlan::REPORT_REJECTED:
                    return response()->json(['message' => 'Kế hoạch đã được hoàn thành không thể từ chối'], 400);
                    break;
                case BusinessPlan::WAITING:
                    break;
            }
            DB::beginTransaction();
            BusinessPlan::where('id', $request->id)->update(['confirm_by' => $user->id, 'status' => BusinessPlan::PLAN_REJECTED]);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessPlanController rejectMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
