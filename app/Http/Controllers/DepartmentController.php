<?php

namespace App\Http\Controllers;

use App\Models\Department;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Transformer\APIJsonResponse;
use App\Models\UserWorking;
use App\Transformer\DepartmentTransformer;

use App\Models\Position;

class DepartmentController extends Controller
{
    public function getAll()
    {
        try {
            $rs = Department::year(date('Y'))
                ->select('*')
                ->with('positions')
                ->orderBy('sort', 'ASC')
                ->get();

            return response()->json($rs);
        } catch (\Exception $e) {
            Log::error("DepartmentController index: " . $e->getMessage());

            return response()->json([], 500);
        }
    }

    public function index(Request $request)
    {
        if ($request->department_id) {
            $data = Department::year(date('Y'))
                ->with('positions')
                ->where('id', $request['department_id'])
                ->orderBy('sort', 'ASC')
                ->orderBy('updated_at', 'DESC')
                ->get();
        } else {
            $data = Department::year(date('Y'))
                ->with('positions')
                ->get();
        }
        return response()->json($data);
    }

    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required'
            ]);

            if ($validator->fails()) {
                return response()->json(['message' => $validator->errors()->first()], 400);
            }

            $model = new Department();
            $model->setYear(date('Y'));
            $msg = 'Thêm phòng ban thành công!';
            if ($request->id) {
                $model = Department::year(date('Y'))->find($request->id);
                $msg = 'Cập nhật phòng ban thành công!';
            }

            $model->name = trim($request->name);
            $model->note = trim($request->note);
            $model->sort = $request->sort;
            $model->save();
            return response()->json(['message' => $msg]);
        } catch (\Exception $e) {
            Log::error("DepartmentController store: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function show($id)
    {
        try {
            $rs = Department::year(date('Y'))->find($id);
            return response()->json($rs);
        } catch (Exception $e) {
            Log::error("DepartmentController show: " . $e->getMessage());

            return response()->json([], 500);
        }
    }

    public function listDepartmentMobile()
    {
        try {
            $department = Department::year(date('Y'))->select('id', 'name', 'code')->orderBy('sort', 'ASC')->get();
            $rs = (new APIJsonResponse)->responseSuccess($department);
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function userDepartmentMobile()
    {
        try {
            $user = auth()->user()->load(['avatar']);
            $role = $user->getRoleNames()->toArray();

            if ($role[0] == "admin" || $role[0] == "hr_leader" || $role[0] == "hr" || $role[0] == "ceo") {
                $departments = Department::year(date('Y'))->select('id', 'name', 'code')->orderBy('sort', 'ASC')->get();
                $rs = (new APIJsonResponse)->responseSuccess($departments);
            } else {
                $department = UserWorking::active()->where('user_id', $user->id)->with('user', 'department', 'position', 'leader', 'subUserWorkings')->first();
                $rs = (new APIJsonResponse)->responseSuccess((new DepartmentTransformer)->transform($department));
            }

        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function userPositionMobile(Request $request)
    {
        try {
            $user = auth()->user()->load(['avatar']);
            $role = $user->getRoleNames()->toArray();

            if ($role[0] == "admin" || $role[0] == "hr_leader" || $role[0] == "hr" || $role[0] == "ceo") {
                $positions = Position::year(date('Y'))->select('id', 'name', 'code', 'is_leader', 'department_id')->groupBy('code')->orderBy('sort', 'ASC')->get();
                $rs = (new APIJsonResponse)->responseSuccess($positions);
            } else {
                $department = UserWorking::active()->where('user_id', $user->id)->with('user', 'department', 'position', 'leader', 'subUserWorkings')->first();

                if ($department->position->is_leader == 1) {
                    $positions = Position::year(date('Y'))->select('id', 'name', 'code', 'is_leader', 'department_id')->whereDepartmentId($department->department_id)->groupBy('code')->orderBy('sort', 'ASC')->get()->first();
                    $positions = [$positions];
                } else {
                    $positions = null;
                }

                $rs = (new APIJsonResponse)->responseSuccess($positions);
            }
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }
}
