<?php

namespace App\Http\Controllers;

use App\Builder\NotificationBuilder;
use App\Builder\NotificationConfig;
use Illuminate\Http\Request;
use App\Models\BusinessTrip;
use App\Models\Response;
use App\Models\User;
use App\Models\UserWorking;
use App\Notifications\BusinessTripNotification;
use App\Events\ConfirmBusinessTripEvent;
use App\Models\SystemConfig;
use App\Providers\UtilityServiceProvider as u;

use App\Transformer\APIJsonResponse;
use App\Transformer\BusinessTripTransformer;
use App\Models\Position;
use App\Services\FirebaseNotification;
use App\Jobs\SendNotifyToAppJob;

class BusinessTripController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $userId = $user->id;
        $isLeader = isset($user->user->position) && $user->user->position->is_leader ? $user->user->position->is_leader : null;
        $departmentId = isset($user->user->department) && $user->user->department->id ? $user->user->department->id : null;
        $roles = $user->getRoleNames();
        $roleName = $roles[0] ?: null;
        $month = $request->month;
        $model = BusinessTrip::month($month);

        if ($roleName != 'admin' && $roleName != 'ceo' && $roleName != 'hr_leader' && $roleName != 'hr') {
            if ($isLeader) {
                $teamUserID = UserWorking::active()->department($departmentId)->pluck('user_id')->toArray();
                $model->whereIn('user_id', $teamUserID);
            } else {
                $model->onlyMe($userId);
            }
        } else {
            if ($request->department_id && !$request->position_id) {
                $teamUserID = UserWorking::active()->department($request->department_id)->pluck('user_id')->toArray();
                $model->whereIn('user_id', $teamUserID);
            }

            if ($request->position_id) {
                $teamUserID = UserWorking::active()->position($request->position_id)->pluck('user_id')->toArray();
                $model->whereIn('user_id', $teamUserID);
            }
        }

        $data = $model->with('user', 'manager', 'position')->orderby('from_date', 'DESC')->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_date' => 'required|before_or_equal:to_date',
            'to_date' => 'required|after_or_equal:from_date',
            'note' => 'required'
        ], [], [
            'from_date' => 'Từ ngày',
            'to_date' => 'Đến ngày',
            'note' => 'Ghi chú',
        ]);
        try {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
            $note = $request->note;
            if ($request->user) {
                $user_id = $request->user['id'];
                $status = BusinessTrip::CONFIRMED;
                $confirm_by = auth()->user()->id;
            } else {
                $user_id = $request->user_id;
                $status = BusinessTrip::PENDING;
                $confirm_by = null;
            }
            $user = UserWorking::where('user_id', $user_id)->where('is_sub_position', false)->where('status', true)->with('user')->first();
            $manager = UserWorking::allActive()->where('position_id', $user->dependent_position_id)->first();
            $name = $user->user->name;
            $position_id = $user->position_id;
            $manager_id = $manager->user_id;
            $data = [
                'user_id' => $user_id,
                'position_id' => $position_id,
                'manager_id' => $manager_id,
                'from_date' => $from_date,
                'to_date' => $to_date,
                'note' => $note,
                'confirm_by' => $confirm_by,
                'status' => $status
            ];
            // return $data;
            BusinessTrip::create($data);
            # bắn notify cho người duyệt
            $confirmUserId = $data['manager_id'] ?: $data['user_id'];
            $userNoti = User::find($confirmUserId);
            $message = [
                'title' => 'Duyệt đăng ký công tác cho nhân viên: ' . $name,
                'link' => '/business-trip',
                'content' => 'Bạn có thông báo duyệt đăng ký công tác cho nhân viên: ' . $name,
                'option' => 'add',
                'type' => '5',
            ];

            $config = new NotificationConfig();
            $config->setMsg($message);
            $config->setUser($userNoti);
            $config->setNotification(function () use ($message) {
                return new BusinessTripNotification($message);
            });
            $config->setBroadcast(function () use ($userNoti) {
                $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\BusinessTripNotification")->first();
                return new ConfirmBusinessTripEvent($userNoti, $newMessage);
            });
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_BUSINESS_TRIP);


            NotificationBuilder::getBuilder()
                ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);

            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\BusinessTrip $BusinessTrip
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'from_date' => 'required|before_or_equal:to_date',
            'to_date' => 'required|after_or_equal:from_date',
            'note' => 'required'
        ], [], [
            'from_date' => 'Từ ngày',
            'to_date' => 'Đến ngày',
            'note' => 'Ghi chú',
        ]);
        try {
            $data = [
                'user_id' => $request->user_id,
                'position_id' => $request->position_id,
                'manager_id' => $request->manager_id,
                'from_date' => $request->from_date,
                'to_date' => $request->to_date,
                'note' => $request->note
            ];
            BusinessTrip::where('id', $id)->update($data);
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param \App\Models\BusinessTrip $BusinessTrip
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        BusinessTrip::where('id', $id)->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $id);
    }

    public function confirm(Request $request, $id)
    {
        try {
            $data = [
                'status' => $request->status,
                'confirm_by' => $request->confirm_by
            ];
            BusinessTrip::where('id', $id)->update($data);

            if ($request->status == 2) {
                # gửi mail thông báo đi công tác cho văn phòng khi đc duyệt
                $model = BusinessTrip::where('id', $id)->with('user:id,name', 'confirm:id,name')->first();
                $rs = SystemConfig::select('content')->notificationHR()->first();
                $receiveEmails = explode(',', $rs->content);
                $contentMails = [];
                foreach ($receiveEmails as $email) {
                    $user = User::select('name')->where('email', $email)->first();
                    $contentMail = (object)[];
                    $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký công tác của nhân sự: " . $model->user->name;
                    $contentMail->receiver = $email;
                    $contentMail->receiver_name = ($user && $user->name) ? $user->name : 'Quý Anh/chị';
                    $contentMail->message = "Nhân sự {$model->user->name} đã được cấp quản lý duyệt đăng ký đi công tác từ ngày: " . vietec_format_date($model->from_date) . " đến ngày: " . vietec_format_date($model->to_date) . ". Người duyệt: " . $model->confirm->name . ".";
                    $contentMails[] = $contentMail;
                }
                if (!isArrEmptyOrNull($contentMails)) {
                    $config = new NotificationConfig();
                    $config->setContentMails($contentMails);
                    NotificationBuilder::getBuilder()
                        ->sendMessage($config, [NotificationBuilder::EMAIL]);
                }
            }

            return Response::formatResponse(config('apicode.SUCCESS'), $id);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function indexMobile(Request $request)
    {
        try {
            $user = $request->user();
            $userId = $user->id;
            $isLeader = isset($user->user->position) && $user->user->position->is_leader ? $user->user->position->is_leader : null;
            $departmentId = isset($user->user->department) && $user->user->department->id ? $user->user->department->id : null;
            $roles = $user->getRoleNames();
            $roleName = $roles[0] ?: null;
            $month = $request->month;
            $model = BusinessTrip::month($month)->orderBy('created_at', 'DESC');

            if ($roleName != 'admin' && $roleName != 'hr') {
                if ($isLeader) {
                    $teamUserID = UserWorking::active()->department($departmentId)->pluck('user_id')->toArray();
                    $model->whereIn('user_id', $teamUserID);
                } else {
                    $model->onlyMe($userId);
                }
            } else {
                $condition = [];
                if (isset($request->department_id)) {
                    $condition['department_id'] = $request->department_id;
                }
                if (isset($request->position_id)) {
                    $condition['position_id'] = $request->position_id;
                }
                $teamUserID = UserWorking::active()->where($condition)->pluck('user_id')->toArray();
                $model->whereIn('user_id', $teamUserID);
            }

            $data = $model->with('user', 'manager', 'position')->orderby('from_date', 'DESC')->get();

            $rs = (new APIJsonResponse)->responseSuccess((new BusinessTripTransformer)->transforms($data));
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function viewAddMobile(Request $request)
    {
        try {
            return response()->json(['description' => 'Hiện tại chức năng này không dùng được trên App, vui lòng sử dụng chức năng trên Web!'], 400);
            $user = auth()->user();
            $staff = UserWorking::active()->where('user_id', $user->id)->first();

            $manager = UserWorking::allActive()
                ->where('position_id', $request->user()->user->leader->id)->with('user')
                ->first();

            if (!$manager) {
                return response()->json(['description' => 'Không tìm thấy thông tin quản lý'], 400);
            }

            $position = Position::year(date('Y'))->select('id', 'name', 'is_leader', 'department_id')->whereId($staff->position_id)->first();

            $arr_view_data = [
                "user_id" => $user->id,
                "staff_code" => $user->staff_code,
                "user_name" => $user->name,
                "position_id" => $staff->position_id,
                "position" => $position->name,
                "from_date" => $request->from_date,
                "to_time" => $request->to_time,
                "status" => 1,
                "note" => "",
                "manager_id" => $manager->user_id,
                "manager_name" => $manager->user->name
            ];

            $rs = (new APIJsonResponse)->responseSuccess($arr_view_data);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function storeMobile(Request $request)
    {
        $user_user = auth()->user();
        $user_id = $user_user->id;
        $request->validate([
            'from_date' => 'required|before_or_equal:to_date',
            'to_date' => 'required|after_or_equal:from_date',
            'note' => 'required'
        ], [], [
            'from_date' => 'Từ ngày',
            'to_date' => 'Đến ngày',
            'note' => 'Ghi chú',
        ]);
        try {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
            $note = $request->note;
            $status = BusinessTrip::PENDING;
            $confirm_by = null;
            $user = UserWorking::where('user_id', $user_id)->where('is_sub_position', false)->where('status', true)->with('user')->first();
            $manager = UserWorking::allActive()->where('position_id', $user->dependent_position_id)->first();
            $name = $user->user->name;
            $position_id = $user->position_id;
            $manager_id = $manager->user_id;
            $data = [
                'user_id' => $user_id,
                'position_id' => $position_id,
                'manager_id' => $manager_id,
                'from_date' => $from_date,
                'to_date' => $to_date,
                'note' => $note,
                'confirm_by' => $confirm_by,
                'status' => $status
            ];

            BusinessTrip::create($data);
            # bắn notify cho người duyệt
            $confirmUserId = $data['manager_id'] ?: $data['user_id'];
            $userNoti = User::find($confirmUserId);
            $message = [
                'title' => 'Duyệt đăng ký công tác cho nhân viên: ' . $name,
                'link' => '/business-trip',
                'content' => 'Bạn có thông báo duyệt đăng ký công tác cho nhân viên: ' . $name,
                'option' => 'add',
                'type' => '5',
            ];
            $config = new NotificationConfig();
            $config->setUser($userNoti);
            $config->setMsg($message);
            $config->setNotification(function () use ($message) {
                return new BusinessTripNotification($message);
            });
            $config->setBroadcast(function () use ($userNoti) {
                $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\BusinessTripNotification")->first();
                return new ConfirmBusinessTripEvent($userNoti, $newMessage);
            });
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_BUSINESS_TRIP);
            NotificationBuilder::getBuilder()
                ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);

            $rs = (new APIJsonResponse)->responseSuccess($data);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function viewUpdateMobile(Request $request)
    {
        try {
            $user = auth()->user();
            $staff = UserWorking::active()->where('user_id', $user->id)->first();

            $manager = UserWorking::allActive()
                ->where('position_id', $request->user()->user->leader->id)->with('user')
                ->first();

            if (!$manager) {
                return response()->json(['description' => 'Không tìm thấy thông tin quản lý'], 400);
            }

            $position = Position::year(date('Y'))->select('id', 'name', 'is_leader', 'department_id')->whereId($staff->position_id)->first();

            $id = $request->id;

            $data = BusinessTrip::where('id', $id)->get()->first();

            $arr_view_data = [
                "user_id" => $user->id,
                "staff_code" => $user->staff_code,
                "user_name" => $user->name,
                "position_id" => $staff->position_id,
                "position" => $position->name,
                "from_date" => $data->from_date,
                "to_date" => $data->to_date,
                "status" => $data->status,
                "note" => $data->note,
                "manager_id" => $manager->user_id,
                "manager_name" => $manager->user->name
            ];

            $rs = (new APIJsonResponse)->responseSuccess($arr_view_data);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function updateMobile(Request $request)
    {
        $request->validate([
            'from_date' => 'required|before_or_equal:to_date',
            'to_date' => 'required|after_or_equal:from_date',
            'note' => 'required'
        ], [], [
            'from_date' => 'Từ ngày',
            'to_date' => 'Đến ngày',
            'note' => 'Ghi chú',
        ]);
        try {
            $id = $request->id;
            $data = [
                'from_date' => $request->from_date,
                'to_date' => $request->to_date,
                'note' => $request->note
            ];

            //            BusinessTrip::where('id', $id)->update($data);
            $model = BusinessTrip::find($id);
            $model->from_date = $request->from_date;
            $model->to_date = $request->to_date;
            $model->note = $request->note;
            $model->save();
            $rs = (new APIJsonResponse)->responseSuccess($data);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }


    public function destroyMobile(Request $request)
    {
        try {
            $id = $request->id;
            BusinessTrip::where('id', $id)->delete();
            $rs = (new APIJsonResponse)->responseSuccess($id);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function confirmMobile(Request $request)
    {
        try {
            $id = $request->id;
            $data = [
                'status' => 2,
                'confirm_by' => auth()->user()->id
            ];
            BusinessTrip::where('id', $id)->update($data);

            if ($request->status == 2) {
                # gửi mail thông báo đi công tác cho văn phòng khi đc duyệt
                $model = BusinessTrip::where('id', $id)->with('user:id,name', 'confirm:id,name')->first();
                $rs = SystemConfig::select('content')->notificationHR()->first();
                $receiveEmails = explode(',', $rs->content);
                $contentMails = [];
                foreach ($receiveEmails as $email) {
                    $user = User::select('name')->where('email', $email)->first();
                    $contentMail = (object)[];
                    $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký công tác của nhân sự: " . $model->user->name;
                    $contentMail->receiver = $email;
                    $contentMail->receiver_name = ($user && $user->name) ? $user->name : 'Quý Anh/chị';
                    $contentMail->message = "Nhân sự {$model->user->name} đã được cấp quản lý duyệt đăng ký đi công tác từ ngày: "
                        . vietec_format_date($model->from_date) . " đến ngày: "
                        . vietec_format_date($model->to_date) . ". Người duyệt: " . $model->confirm->name . ".";
                    $contentMails[] = $contentMail;
                }
                if (!isArrEmptyOrNull($contentMails)) {
                    $config = new NotificationConfig();
                    $config->setContentMails($contentMails);
                    NotificationBuilder::getBuilder()
                        ->sendMessage($config, [NotificationBuilder::EMAIL]);
                }
            }

            $rs = (new APIJsonResponse)->responseSuccess($id);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function rejectMobile(Request $request)
    {
        try {
            $id = $request->id;
            $data = [
                'status' => 3,
                'confirm_by' => auth()->user()->id
            ];
            BusinessTrip::where('id', $id)->update($data);

            $rs = (new APIJsonResponse)->responseSuccess($id);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }
}
