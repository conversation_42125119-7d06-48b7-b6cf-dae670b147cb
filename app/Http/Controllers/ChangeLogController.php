<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChangeLog;
use App\Models\Response;
use App\Models\SystemConfig;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use App\Notifications\ChangeLogNotification;
use App\Builder\NotificationConfig;
use App\Builder\NotificationBuilder;
use App\Events\ConfirmChangeLogEvent;
use App\Services\FirebaseNotification;
use Exception;
use Illuminate\Support\Facades\Log;

class ChangeLogController extends Controller
{
    public function index(Request $request)
    {
        try {
            $department_id = $request->department_id;
            $category_id = $request->category_id;
            $product_category_id = $request->product_category_id;
            $rank_code = $request->rank_code;
            $status = $request->status;
            $date = $request->date;
            $keyword = $request->keyword;
            $query = ChangeLog::with(['user', 'owner.avatar', 'owner.user', 'department', 'position', 'owner.user.userLeader.user.avatar', 'product_category']);
            ChangeLog::getConditionList($request, $query, $department_id, $rank_code, $category_id, $status, $date, $keyword, $product_category_id);
            $result = $query->orderBy('date', 'DESC')->paginate(20);
            $data = [
                'data' => $result
            ];
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            Log::error('ChangeLogController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function detail(Request $request)
    {
        try {
            $data = ChangeLog::with(['user', 'product_category'])->where('id', $request->id)->first();
            if (!$data) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu'], 404);
            } else {
                return response()->json(['data' => $data]);
            }
            // if ($data->user_id == auth()->user()->id) {
            //     return response()->json(['data' => $data]);
            // }else {
            //     return response()->json(['message' => 'Bạn không có quyền xem nhật ký của nhân viên này'], 403);
            // }
        } catch (\Exception $e) {
            Log::error('ChangeLogController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'content' => 'required',
            'date' => 'required|date_format:Y-m-d'
        ], [], [
            'title' => 'Tiêu đề',
            'content' => 'Nội dung',
            'date' => 'Ngày'
        ]);
        try {
            $title = $request->title;
            $category_id = (int)$request->category_id;
            $product_category_id = $request->product_category_id;
            $content = $request->content;
            $status = $request->status;
            $date = $request->date;
            $change_log = ChangeLog::where('user_id', auth()->user()->id)
                ->whereDate('date', $date)
                ->where('category_id', $category_id)
                ->where('product_category_id', $product_category_id)
                ->first();
            $category_name = $category_id > 0 ? 'trong danh mục ' . ChangeLog::CATEGORIES[$category_id] . ' ' : null;
            $message = 'Bạn đã thêm nhật ký ' . $category_name . 'cho ngày này!';
            if ($change_log) {
                return response()->json(['message' => $message], 404);
            }
            DB::beginTransaction();
            $changeLog = new ChangeLog();
            $changeLog->user_id = auth()->user()->id;
            $changeLog->department_id = auth()->user()->user->department_id;
            $changeLog->position_id = auth()->user()->user->position_id;
            $changeLog->level_position = auth()->user()->user->position && auth()->user()->user->position->is_leader ? changeLog::LEADER_POSITION : changeLog::STAFF_POSITION;
            $changeLog->date = $date;
            $changeLog->category_id = $category_id;
            $changeLog->product_category_id = $product_category_id;
            $changeLog->title = $title;
            $changeLog->content = $content;
            $changeLog->status = $status;
            $changeLog->save();
            DB::commit();
            if ($status > 0) {
                $this->sendNotification($request, $changeLog);
            }
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ChangeLogController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function update(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'content' => 'required',
            'date' => 'required|date_format:Y-m-d'
        ], [], [
            'title' => 'Tiêu đề',
            'content' => 'Nội dung',
            'date' => 'Ngày'
        ]);
        try {
            $model = ChangeLog::where('id', $request->id)->first();

            DB::beginTransaction();

            $title = $request->title;
            $category_id = (int)$request->category_id;
            $product_category_id = $request->product_category_id;
            $content = $request->content;
            $status = $request->status;
            $date = $request->date;
            $publish = $request->publish;

            $model->date = $date;
            $model->category_id = $category_id;
            $model->product_category_id = $product_category_id;
            $model->title = $title;
            $model->content = $content;
            $model->status = $status;
            $model->save();
            DB::commit();
            if ($publish) {
                $this->sendNotification($request, $model);
            }
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("KPIController update: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $log = ChangeLog::find($id);
            if (!$log) {
                return response()->json(['message' => 'Không tìm thấy nhật ký!'], 404);
            }
            if ($log->user_id != auth()->user()->id) {
                return response()->json(['message' => 'Bạn không có quyền xóa bản ghi này!'], 400);
            }
            DB::beginTransaction();
            $log->delete();
            DB::commit();
            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ContractController destroy: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getCategories()
    {
        return ChangeLog::categories();
    }

    public function sendNotification($request, $changeLog)
    {
        $config = SystemConfig::where('type', SystemConfig::INFORMATION_CHANGELOG)->first();
        $receiver = explode(',', $config->content);
        if (in_array('all', $receiver)) {
            $userNotis = User::notAdmin()->where('status', 1)->get();
        } else {
            $userNotis = User::whereIn('email', $receiver)->notAdmin()->get();
        }
        $new_log = ChangeLog::with('product_category')->where('id', $changeLog->id)->first();
        $message = [
            'title' => '[SẢN PHẨM] - ' . $new_log->title,
            'link' => '/changelog-detail/' . $new_log->id,
            'content' => $new_log->content,
            'option' => 'add',
            'type' => '1',
        ];
        foreach ($userNotis as $userNoti) {
            // bắn notify
            $config = new NotificationConfig();
            $config->setMsg($message);
            $config->setUser($userNoti);
            $config->setNotification(function () use ($message) {
                return new ChangeLogNotification($message);
            });
            $config->setBroadcast(function () use ($userNoti) {
                $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\ChangeLogNotification")->first();
                return new ConfirmChangeLogEvent($userNoti, $newMessage);
            });
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_NOTIFICATION);
            // bắn mail
            $contentMail = (object)[];
            $contentMail->subject = "[SẢN PHẨM]- " . $new_log->title;
            $contentMail->receiver = env('APP_ENV') == 'production' ? $userNoti['email'] : '<EMAIL>';
            $contentMail->cc = env('APP_ENV') == 'production' ? $userNoti['email'] : '<EMAIL>';
            $contentMail->receiver_name = $userNoti['name'];
            $contentMail->title = $new_log->title;
            $contentMail->message = $new_log->content;
            $contentMail->link = env('APP_URL') . '/changelog-detail/' . $new_log->id;
            $contentMail->product_category = $new_log->product_category ? $new_log->product_category['code'] : '';
            $config->setContentMails($contentMail);
            NotificationBuilder::getBuilder()
                ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT, NotificationBuilder::CHANGE_LOG]);
        }
    }
}
