<?php

namespace App\Http\Controllers;

use App\Models\ScreenCategory;
use App\Models\ScreenRole;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class AppDisplayController extends Controller
{
    public function index()
    {
        $data = ScreenCategory::select('id', 'parent_id', 'title')->where('parent_id', null)->with('screen_childs')->get();
        return $data;
    }

    public function update(Request $request, $id)
    {
        try {
            $screen_selected = [];
            $new_screens = [];
            foreach ($request->all() as $screen) {
                array_push($screen_selected, $screen, ['id' => $screen['parent_id'], 'parent_id' => null, 'title' => null]);
            }
            foreach ($screen_selected as $e) {
                if (!in_array($e, $new_screens)) {
                    array_push($new_screens, $e);
                }
            }
            DB::beginTransaction();
            ScreenRole::where('role_id', $id)->delete();
            foreach ($new_screens as $e) {
                $modal = new ScreenRole();
                $modal->role_id = $id;
                $modal->screen_id = $e['id'];
                $modal->screen_parent_id = $e['parent_id'];
                $modal->save();
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('AppDisplayController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function show($id)
    {
        $query = ScreenRole::where('role_id', $id)->whereNotNull('screen_parent_id')->with('screen')->get();
        $data = [];
        foreach ($query as $e) {
            $data[] = [
                'id' => $e->screen_id,
                'parent_id' => $e->screen_parent_id,
                'title' => $e->screen->title
            ];
        }
        return $data;
    }

    public function showMobile()
    {
        $role_id = ScreenRole::getRoleId();
        $data = ScreenRole::where('role_id', $role_id)->whereNull('screen_parent_id')
            ->whereHas('screen_role_childs.screen')
            ->with('screen_role_childs.screen', 'screen')->get();
        return response()->json(['data' => $data]);
    }
}
