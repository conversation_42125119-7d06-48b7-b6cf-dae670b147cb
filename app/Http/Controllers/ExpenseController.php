<?php

namespace App\Http\Controllers;

use App\Http\Requests\ExpenseStoreRequest;
use App\Http\Requests\ExpenseUpdateRequest;
use App\Models\Expense;
use App\Models\ExpenseClient;
use App\Models\ExpenseInput;
use Illuminate\Http\Request;
use App\Models\Response;
use Illuminate\Support\Facades\DB;

class ExpenseController extends Controller
{
    //get list
    public function index(Request $request)
    {
        $query = Expense::with('sale', 'team');
        $data = $query->paginate(15);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
    //store expenses
    public function store(ExpenseStoreRequest $request)
    {
        try {
            $expense = Expense::create($request->expense);
            $expense_id = $expense->id;
            foreach ($request->array_expenses as $expense) {
                $expense['expense_id'] = $expense_id;
                ExpenseInput::create($expense);
            }
            //create expense client
            $this->updateClient($request->all(), $expense_id);
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            return $e;
        }
    }
    public function show(Request $request)
    {
        $expense_id = $request->id;
        try {
            $data = Expense::where('id', $expense_id)->with(
                'sale',
                'team',
                'expense_inputs',
                'client_provinces.provinces',
                'client_districts.districts',
                'client_schools.clients.school'
            )->first();
            $client_types = ExpenseClient::select('type')->where('expense_id', 7)->groupBy('type')->get();
            $types = [];
            foreach ($client_types as $type) {
                array_push($types, $type['type']);
            }
            $data['types'] = $types;
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            return $e;
        }
    }
    public function update(ExpenseUpdateRequest $request)
    {
        $expense_id = $request->id;
        try {
            DB::beginTransaction();
            Expense::where('id', $expense_id)->update($request->expense);
            $expense_input_ids = [];
            foreach ($request->array_expenses as $e) {
                if (isset($e['id'])) {
                    ExpenseInput::where('id', $e['id'])->update($e);
                    array_push($expense_input_ids, $e['id']);
                } else {
                    $e['expense_id'] = $expense_id;
                    $new_ex = ExpenseInput::create($e);
                    array_push($expense_input_ids, $new_ex['id']);
                }
            }
            ExpenseInput::where('expense_id', $expense_id)->whereNotIn('id', $expense_input_ids)->delete();
            ExpenseClient::where('expense_id', $expense_id)->delete();
            //update client
            $this->updateClient($request->all(), $expense_id);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
    public function updateClient($request, $expense_id)
    {
        foreach ($request['client_selected'] as $client) {
            if ($client == 1) {
                ExpenseClient::create([
                    'expense_id' => $expense_id,
                    'type' => 1
                ]);
            }
            if ($client == 2 && $request['provinces']) {
                foreach ($request['provinces'] as $province) {
                    ExpenseClient::create([
                        'expense_id' => $expense_id,
                        'type' => 2,
                        'province_id' => $province['province_id']
                    ]);
                }
            }
            if ($client == 3 && $request['districts']['districts']) {
                foreach ($request['districts']['districts'] as $district) {
                    ExpenseClient::create([
                        'expense_id' => $expense_id,
                        'type' => 3,
                        'province_id' => $request['districts']['province_id'],
                        'district_id' => $district['district_id']
                    ]);
                }
            }
            if ($client == 4 && $request['schools']) {
                foreach ($request['schools'] as $client) {
                    ExpenseClient::create([
                        'expense_id' => $expense_id,
                        'type' => 4,
                        'school_id' => $client['id']
                    ]);
                }
            }
        }
    }
    public function destroy(Request $request)
    {
        $expense_id = $request->id;
        try {
            Expense::where('id', $expense_id)->delete();
            ExpenseInput::where('expense_id', $expense_id)->delete();
            ExpenseClient::where('expense_id', $expense_id)->delete();
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            return $e;
        }
    }
}
