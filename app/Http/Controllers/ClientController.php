<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Department;
use App\Models\TeamSaleArea;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\ClientStoreRequest;
use App\Http\Requests\ClientUpdateRequest;
use App\Models\ClientDetail;
use App\Models\Contract;
use App\Models\DistrictBusinessMarket;
use App\Transformer\ClientTransformer;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\DB;
use App\Models\Response;
use Illuminate\Support\Str;
use App\Transformer\APIJsonResponse;

class ClientController extends Controller
{

    public function index(Request $request)
    {
        try {
            # check lại quyền khu vực kinh doanh đối với từng team
            $districtIds = [];
            $provinceIds = [];
            $user = $request->user();

            if ($user->roles[0]->name != 'admin') {
                if ($user->user && $user->user->position && $user->user->position->department_id == Department::SALE_DEPARTMENT) {
                    $area = TeamSaleArea::getProvincesAndDistrictsBySaleArea(auth()->user());
                    $provinceIds = array_unique($area['provinceIds']);
                    $districtIds = array_unique($area['districtIds']);
                    if (
                        $request->province_id && !in_array($request->province_id, $provinceIds)
                        || $request->district_id && !in_array($request->district_id, $districtIds)
                    ) {
                        return response()->json(['message' => 'Bạn không có quyền truy cập thị trường này!'], 403);
                    }
                }
            }

            # query get data
            $query = Client::with('provinceBusinessMarket', 'districtBusinessMarket', 'contracts')
                ->withCount('contracts');
            $this->getCondition($request, $query);

            if ($user->roles[0]->name != 'admin') {
                if (!$request->province_id && !$request->district_id && $user->user->position->department_id == Department::SALE_DEPARTMENT) {
                    $query->whereIn('district_id', $districtIds);
                }
            }

            $rs = $query->orderBy('created_at', 'DESC')->paginate(20)->through(function ($item) {
                return (new ClientTransformer)->transform($item);
            });
            return response()->json(['data' => $rs]);
        } catch (\Exception $e) {
            Log::error("ClientController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(ClientStoreRequest $request)
    {
        try {
            $client = new Client();
            $client->code = $request->code;
            $client->school_id = $request->school_id;
            $client->type = $request->type;
            $client->province_id = $request->province_id;
            $client->district_id = $request->district_id;
            $client->name = $request->name;
            $client->address = $request->address;
            $client->phone_number = $request->phone_number;
            $client->note = $request->note ?: null;
            $client->tax_code = $request->tax_code ?: null;
            $client->accountant_name = $request->accountant_name ?: null;
            $client->accountant_phone = $request->accountant_phone ?: null;
            $client->head_master_name = $request->head_master_name ?: null;
            $client->bank_name = $request->bank_name ?: null;
            $client->bank_account_number = $request->bank_account_number ?: null;
            $client->creator_id = auth()->user()->id;
            $client->editor_id = auth()->user()->id;
            $client->save();
            return response()->json(['message' => 'Create Success'], 200);
        } catch (\Exception $e) {
            Log::error('ClientController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function update(ClientUpdateRequest $request)
    {
        try {
            $id = $request->id;
            $client = Client::find($id);
            if (!$client) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu!'], 404);
            }
            //check xem có hợp đồng liên quan không
            $contract = Contract::where('client_id', $id)->count();
            if ($contract > 0) {
                return response()->json(['message' => 'Khách hàng này đã có hợp đồng!'], 422);
            }
            $client->code = $request->code;
            $client->school_id = $request->school_id;
            $client->type = $request->type;
            $client->province_id = $request->province_id;
            $client->district_id = $request->district_id;
            $client->name = $request->name;
            $client->address = $request->address;
            $client->phone_number = $request->phone_number;
            $client->note = $request->note ?: null;
            $client->tax_code = $request->tax_code ?: null;
            $client->accountant_name = $request->accountant_name ?: null;
            $client->accountant_phone = $request->accountant_phone ?: null;
            $client->head_master_name = $request->head_master_name ?: null;
            $client->bank_name = $request->bank_name ?: null;
            $client->bank_account_number = $request->bank_account_number ?: null;
            $client->editor_id = auth()->user()->id;
            $client->save();
            return response()->json(['message' => 'Update Success'], 200);
        } catch (\Exception $e) {
            Log::error('ClientController update: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function destroy(Client $client)
    {
        try {
            $client->delete();
            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            Log::error('ClientController destroy: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function details(Client $client)
    {
        try {
            $id = $client->id;
            $data = Client::where('id', $id)->with('provinceBusinessMarket', 'districtBusinessMarket')->first();
            return response()->json((new ClientTransformer)->detail($data));
        } catch (\Exception $e) {
            Log::error('ClientController details: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getTypeClient()
    {
        $rs = [];
        $data = Client::TYPE;
        foreach ($data as $k => $v) {
            $rs[] = (object) ["id" => $k, "name" => $v];
        }
        return response()->json($rs);
    }

    public function searchForContract(Request $request)
    {
        try {
            # chưa dám xoá vì ko biết có ai all đến ko

            return response()->json(['data' => []]);
        } catch (\Exception $e) {
            Log::error("ClientController index: " . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function exportClient(Request $request)
    {
        $inputFileName = 'templates/contract_report.xlsx';
        $fileType = IOFactory::identify($inputFileName);
        $objReader = IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);
        $row = 11;
        $query = Client::with('provinceBusinessMarket');
        $this->getCondition($request, $query);
        $contracts = $query->get();
        foreach ($contracts as $k => $e) {
            $sheet->setCellValue('A' . $row, $k + 1)->getStyle('A' . $row)->getAlignment()->setHorizontal('center');
            $sheet->setCellValue('J' . $row, $e->code);
            $sheet->setCellValue('K' . $row, $e->name);
            $sheet->setCellValue('L' . $row, $e->tax_code);
            $sheet->setCellValue('M' . $row, $e->address);
            $sheet->setCellValue('P' . $row, $e->accountant_name);
            $sheet->setCellValue('Q' . $row, $e->accountant_phone);
            $sheet->setCellValue('S' . $row, $e->phone_number);
            $sheet->setCellValue('U' . $row, $e->districtBusinessMarket->code);
            $sheet->setCellValue('V' . $row, $e->districtBusinessMarket->name);
            $sheet->setCellValue('X' . $row, $e->provinceBusinessMarket->name);
            $row++;
        }
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="Report.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (\Exception $exception) {
            throw $exception;
        }
    }

    private function getCondition(Request $request, $query)
    {
        $query->when($request->keyword, function ($subQuery, $keyword) {
            return $subQuery->searchTextLike($keyword);
        });
        $query->when($request->type, function ($subQuery, $type) {
            return $subQuery->where('type', $type);
        });
        $query->when($request->province_id, function ($subQuery, $province_id) {
            return $subQuery->where('province_id', $province_id);
        });
        $query->when($request->district_id, function ($subQuery, $district_id) {
            return $subQuery->where('district_id', $district_id);
        });
    }

    public function downloadFileSample()
    {
        $file = public_path() . '/templates/ClientSample.xlsx';
        return response()->download($file);
    }

    public function importExcel(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|file|mimes:xls,xlsx'
        ]);
        $file = $request->file('file');
        try {

            $spreadsheet = IOFactory::load($file->getRealPath());
            $sheet        = $spreadsheet->getActiveSheet(0);
            $row_limit    = $sheet->getHighestDataRow();
            $row_range    = range(4, $row_limit);
            DB::beginTransaction();
            $errors = [];
            $clients = [];
            foreach ($row_range as $row) {
                if (!$sheet->getCell('A' . $row)->getValue()) {
                    $errors[] = "Loại khách hàng dòng " . $row . " không được bỏ trống";
                }
                if (!$sheet->getCell('B' . $row)->getValue()) {
                    $errors[] = "Mã khách hàng dòng " . $row . " không được bỏ trống";
                }
                $exist_client = Client::where('code', $sheet->getCell('B' . $row)->getValue())->first();
                if ($exist_client) {
                    $errors[] = "Mã khách hàng dòng " . $row . " đã tồn tại";
                }
                if (!$sheet->getCell('C' . $row)->getValue()) {
                    $errors[] = "Tên khách hàng dòng " . $row . " không được bỏ trống";
                }
                if (!$sheet->getCell('E' . $row)->getValue()) {
                    $errors[] = "Nhóm KH, NCC dòng " . $row . " không được bỏ trống";
                }

                $district = DistrictBusinessMarket::where('code', $sheet->getCell('E' . $row)->getValue())->first();
                if (!$district) {
                    $errors[] = "Nhóm KH, NCC dòng " . $row . " không tồn tại";
                }
                if (count($errors) > 0) {
                    return response()->json($errors, 422);
                }
                //check type
                $val_type = trim($sheet->getCell('A' . $row)->getValue());

                if ($val_type == Client::TYPE[Client::BRANCH]) {
                    $type = Client::BRANCH;
                } elseif ($val_type == Client::TYPE[Client::SCHOOL]) {
                    $type = Client::SCHOOL;
                } elseif ($val_type == Client::TYPE[Client::PERSONAL]) {
                    $type = Client::PERSONAL;
                } else {
                    $type = Client::OTHER;
                }

                $client = [
                    'id' => Str::uuid()->toString(),
                    'type' => $type,
                    'province_id' => $district->province_id,
                    'district_id' => $district->district_id,
                    'address' => $sheet->getCell('D' . $row)->getValue(),
                    'name' => $sheet->getCell('C' . $row)->getValue(),
                    'name_upper' => strtoupper($sheet->getCell('C' . $row)->getValue()),
                    'code' => $sheet->getCell('B' . $row)->getValue(),
                    'tax_code' => $sheet->getCell('F' . $row)->getValue(),
                    // 'head_master_name' => $sheet->getCell('H' . $row)->getValue(),
                    'phone_number' => $sheet->getCell('G' . $row)->getValue(),
                    // 'accountant_name' => $sheet->getCell('J' . $row)->getValue(),
                    'accountant_phone' => $sheet->getCell('H' . $row)->getValue(),
                    // 'bank_name' => $sheet->getCell('L' . $row)->getValue(),
                    // 'bank_account_number' => $sheet->getCell('M' . $row)->getValue(),
                    // 'note' => $sheet->getCell('N' . $row)->getValue(),
                    'creator_id' => auth()->user()->id,
                ];
                array_push($clients, $client);
            }

            Client::insert($clients);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            Log::error('ClientController update: ' . $e->getMessage());
            DB::rollBack();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }
    public function searchClient(Request $request)
    {
        try {
            $client = Client::searchClient($request->province_id, $request->district_id, $request->client_code);
            return $client;
        } catch (\Exception $e) {
            Log::error("ClientController searchClient: " . $e->getMessage());
            return response()->json([], 500);
        }
    }
    // mobile
    public function indexMobile(Request $request)
    {
        try {
            # check lại quyền khu vực kinh doanh đối với từng team
            $districtIds = [];
            $provinceIds = [];
            $user = $request->user();
            if ($user->user && $user->user->position && $user->user->position->department_id == Department::SALE_DEPARTMENT) {
                $area = TeamSaleArea::getProvincesAndDistrictsBySaleArea(auth()->user());
                $provinceIds = array_unique($area['provinceIds']);
                $districtIds = array_unique($area['districtIds']);
                if (
                    $request->province_id && !in_array($request->province_id, $provinceIds)
                    || $request->district_id && !in_array($request->district_id, $districtIds)
                ) {
                    $errors = 'Bạn không có quyền truy cập thị trường này!';
                    return (new APIJsonResponse)->responseError(403, $errors, null, null);
                }
            }

            # query get data
            $query = Client::with('provinceBusinessMarket', 'districtBusinessMarket', 'contracts')
                ->withCount('contracts');
            $this->getCondition($request, $query);

            if (!$request->province_id && !$request->district_id && $user->user->position->department_id == Department::SALE_DEPARTMENT) {
                $query->whereIn('district_id', $districtIds);
            }

            $rs = $query->orderBy('created_at', 'DESC')->paginate(10);
            $pre_page = $request['pre_page'] ?? 10;
            $option = [
                'current_page' => $rs->currentPage(),
                'per_page' => $pre_page,
                'total' => $rs->lastpage()
            ];
            return (new APIJsonResponse)->responseSuccess((new ClientTransformer)->transforms($rs), null, $option);
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function ClientFocalOfficerStore(Request $request, $id)
    {
        $request->validate([
            'name' => 'required',
            'gender' => 'required|numeric',
            'phone_number' => 'required|numeric',
            'product_category_id' => 'required',
            'user_name' => 'required',
        ], [], [
            'name' => 'Họ tên',
            'gender' => 'Giới tính',
            'phone_number' => 'Số điện thoại',
            'product_category_id' => 'Dự án',
            'user_name' => 'Tên đăng nhập',
        ]);
        try {
            DB::beginTransaction();
            $data = (object)$request->all();
            $modal = new ClientDetail();
            $modal->client_id = $id;
            $modal->name = $data->name;
            $modal->gender = $data->gender;
            $modal->phone_number = $data->phone_number;
            $modal->email = $data->email;
            $modal->product_category_id = $data->product_category_id;
            $modal->user_name = $data->user_name;
            $modal->note = $data->note;
            $modal->save();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ClientController ClientFocalOfficerStore: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function ClientFocalOfficerUpdate(Request $request, $id)
    {
        $request->validate([
            'name' => 'required',
            'gender' => 'required|numeric',
            'phone_number' => 'required|numeric',
            'product_category_id' => 'required',
            'user_name' => 'required',
        ], [], [
            'name' => 'Họ tên',
            'gender' => 'Giới tính',
            'phone_number' => 'Số điện thoại',
            'product_category_id' => 'Dự án',
            'user_name' => 'Tên đăng nhập',
        ]);
        try {
            DB::beginTransaction();
            $data = (object) $request->all();
            $modal = ClientDetail::find($id);
            $modal->name = $data->name;
            $modal->gender = $data->gender;
            $modal->phone_number = $data->phone_number;
            $modal->email = $data->email;
            $modal->product_category_id = $data->product_category_id;
            $modal->user_name = $data->user_name;
            $modal->note = $data->note;
            $modal->save();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ClientController ClientFocalOfficerUpdate: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function ClientFocalOfficerDelete($id)
    {
        try {
            DB::beginTransaction();
            ClientDetail::where('id', $id)->delete();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ClientController ClientFocalOfficerDelete: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getClientDetail($id)
    {
        try {
            return ClientDetail::where('client_id', $id)->with('product_category')->get();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ClientController getClientDetail: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
