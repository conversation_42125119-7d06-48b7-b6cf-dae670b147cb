<?php

namespace App\Http\Controllers;

use App\Models\BusinessComment;
use App\Models\BusinessPlan;
use App\Models\BusinessPlanDetail;
use App\Models\BusinessReport;
use App\Models\BusinessReportDetail;
use App\Models\UserWorking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Response;
use App\Builder\NotificationBuilder;
use App\Builder\NotificationConfig;
use App\Models\AdditionalBusinessCost;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\IOFactory;

class BusinessReportController extends Controller
{
    public function list(Request $request)
    {
        $date = explode('-', $request->month);
        $month = $date[0];
        $year = $date[1];
        $query = BusinessPlan::whereMonth('to_date', $month)->whereYear('to_date', $year)
            ->whereIn('status', [BusinessPlan::PLAN_APPROVED, BusinessPlan::REPORT_UPDATE, BusinessPlan::REPORT_APPROVED, BusinessPlan::REPORT_REJECTED])
            ->when($request->department_id, function ($q, $departmentId) {
                $q->whereHas('created_by.endOfWorking', function ($subQuery) use ($departmentId) {
                    return $subQuery->where('department_id', $departmentId);
                });
            })->when($request->status, function ($q, $status) {
                $q->where('status', $status);
            });
        $dt = $query->with('created_by', 'confirm_by')->orderBy('from_date', 'DESC')->paginate(20);
        $status = BusinessPlan::convertStatus();
        $data = [
            'data' => $dt,
            'status' => $status
        ];
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function show($id)
    {
        $data_plan = BusinessPlan::where('id', $id)->with('plans', 'additional_costs', 'comments.created_by', 'partners')->first();
        $data_report = BusinessReport::where('business_plan_id', $id)->with('plans', 'additional_costs')->first();
        if (!$data_report) {
            $data_report = $data_plan;
        }
        $ranks = BusinessReport::convertRank();
        $isShowBtnSave = BusinessReport::showBtnSave($data_plan);
        $disabledSelected = BusinessReport::disabledSelected($data_plan);
        $isShowApprove = BusinessReport::showBtnApprove($data_plan);
        return (object)[
            'data_plan' => $data_plan,
            'data_report' => $data_report,
            'disabledSelected' => $disabledSelected,
            'isShowBtnSave' => $isShowBtnSave,
            'ranks' => $ranks,
            'isShowApprove' => $isShowApprove
        ];
    }

    //NV cập nhật báo cáo công tác
    public function update(Request $request, $id)
    {
        $request->validate([
            'data.from_date' => 'required',
            'data.to_date' => 'required',
            'data.expense' => 'required|integer',
            'data.stay' => 'required|integer',
            'data.vehicle' => 'required',
            'data.staffs' => 'required',
            'data.plans.*.name' => 'required',
            'data.plans.*.content' => 'required',
            'data.additional_costs.*.amount' => 'required',
            'data.additional_costs.*.content' => 'required',
        ], [], [
            'data.to_date' => 'Đến ngày',
            'data.from_date' => 'Từ ngày',
            'data.expense' => 'Công tác phí',
            'data.stay' => 'Lưu trú',
            'data.vehicle' => 'Phương tiện',
            'data.staffs' => 'Nhóm công tác',
            'data.plans.*.name' => 'Kế hoạch',
            'data.plans.*.content' => 'Nội dung',
            'data.additional_costs.*.amount' => 'Số tiền',
            'data.additional_costs.*.content' => 'Nội dung',
        ]);
        try {

            $data = (object)$request->data;
            $from_date = $data->from_date;
            $to_date = $data->to_date;
            $staffs = [];
            foreach ($data->staffs as $e) {
                $staff = (object)$e;
                $staffs[] = [
                    'id' => $staff->id,
                    'name_code' => $staff->name_code
                ];
            }
            if (auth()->user()->id != $data->created_by || $data->status != BusinessPlan::PLAN_APPROVED) {
                return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
            }
            DB::beginTransaction();
            $business_report = new BusinessReport();
            $business_report->business_plan_id = $id;
            $business_report->from_date = $from_date;
            $business_report->to_date = $to_date;
            $business_report->expense = $data->expense;
            $business_report->stay = $data->stay;
            $business_report->other_costs = $data->other_costs;
            $business_report->vehicle = json_encode($data->vehicle);
            $business_report->staffs = json_encode($staffs);
            $business_report->created_by = auth()->user()->id;
            $business_report->exception = $data->exception;
            $business_report->save();

            //store business report detail
            foreach ($data->plans as $e) {
                $plan = (object)$e;
                $business_report_detail = new BusinessReportDetail();
                $business_report_detail->business_report_id = $business_report->id;
                $business_report_detail->name = $plan->name;
                $business_report_detail->content = $plan->content;
                $business_report_detail->save();
            }

            AdditionalBusinessCost::store($data, $id, BusinessPlan::REPORT_TYPE);
            BusinessPlan::where('id', $id)->update(['status' => BusinessPlan::REPORT_UPDATE]);
            BusinessReport::where('business_plan_id', $id)->update(['status' => BusinessPlan::REPORT_UPDATE]);

            //send email thông báo cho leader
            $leader = BusinessPlan::getLeader(auth()->user()->id);
            $config = new NotificationConfig();
            $config->setUser($leader);
            $contentMail = (object)[];
            $contentMail->subject = "[VIETEC-QLDN]- Thông báo duyệt báo cáo công tác cho nhân sự : " . auth()->user()->name;
            $contentMail->receiver = env('APP_ENV') == 'production' ? $leader->email : '<EMAIL>';
            $contentMail->receiver_name = $leader->name;
            $contentMail->message = "Anh/chị vui lòng duyệt báo cáo công tác của nhân sự " . auth()->user()->name . ' Từ ngày ' . Carbon::parse($data->from_date)->format('d-m-Y') . ' Đến ngày ' . Carbon::parse($data->to_date)->format('d-m-Y');
            $config->setContentMails([$contentMail]);
            NotificationBuilder::getBuilder()->sendMessage($config, [NotificationBuilder::EMAIL]);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessReportController update: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function approve($id)
    {
        try {
            DB::beginTransaction();
            $plan = BusinessPlan::where('id', $id)->first();
            $leader = BusinessPlan::getLeader($plan->created_by);
            if ($leader->id != auth()->user()->id || $plan->status != BusinessPlan::REPORT_UPDATE) {
                return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
            }
            BusinessPlan::where('id', $id)->update(['confirm_by' => auth()->user()->id, 'status' => BusinessPlan::REPORT_APPROVED]);
            BusinessReport::where('business_plan_id', $id)->update(['confirm_by' => auth()->user()->id, 'status' => BusinessPlan::REPORT_APPROVED]);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessReportController reportApproved: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function reject($id)
    {
        try {
            DB::beginTransaction();
            $plan = BusinessPlan::where('id', $id)->first();
            $leader = BusinessPlan::getLeader($plan->created_by);
            if ($leader->id != auth()->user()->id || $plan->status != BusinessPlan::REPORT_UPDATE) {
                return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
            }
            BusinessPlan::where('id', $id)->update(['confirm_by' => auth()->user()->id, 'status' => BusinessPlan::REPORT_REJECTED]);
            BusinessReport::where('business_plan_id', $id)->update(['confirm_by' => auth()->user()->id, 'status' => BusinessPlan::REPORT_REJECTED]);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessReportController rejectApproved: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function comment(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            //check xem là leader và trạng thái TP đã đánh giá mới lưu đánh giá xếp loại chuyến công tác
            $business_plan = BusinessPlan::where('id', $id)->first();
            if ($request->rank && $business_plan->status == BusinessPlan::REPORT_APPROVED && $business_plan->confirm_by == auth()->user()->id) {
                BusinessPlan::where('id', $id)->update(['rank' => $request->rank]);
                BusinessReport::where('business_plan_id', $id)->update(['rank' => $request->rank, 'confirm_by' => auth()->user()->id]);
            }

            if ($request->comment) {
                $business_comment = new BusinessComment();
                $business_comment->business_plan_id = $id;
                $business_comment->comment = $request->comment;
                $business_comment->created_by = auth()->user()->id;
                $business_comment->save();
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessReportController store: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function commentList($id)
    {
        $comments = BusinessComment::where('business_plan_id', $id)->where('created_by', auth()->user()->id)->with('created_by')->latest()->first();
        return $comments;
    }

    public function exportRequestComplete($id)
    {
        $plan = BusinessPlan::where('id', $id)->with('businessReport', 'additional_costs', 'plans')->first();
        $staff_arr = [];
        $count_staff = count($plan->staffs);
        foreach ($plan->staffs as $staff) {
            array_push($staff_arr, $staff['name_code']);
        }

        $total_plan = array_sum([$plan->expense, $plan->stay, $plan->other_costs]);
        if ($plan->status != BusinessPlan::REPORT_APPROVED || $plan->created_by != auth()->user()->id) {
            return response()->json(['message' => 'Bạn không có quyền thao tác bản ghi này!'], 403);
        }
        $inputFileName = 'templates/DE_NGHI_HOAN_UNG.xlsx';
        $fileType = IOFactory::identify($inputFileName);
        $objReader = IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);
        $row = 22;
        $sheet->setCellValue('A6', 'Ngày ' . date("d") . ' tháng ' . date("m") . ' năm ' . date("Y"))->getStyle('C6')->getAlignment()->setHorizontal('center');
        $sheet->setCellValue('A9', 'Họ và tên: ' . auth()->user()->name);
        $sheet->setCellValue('A10', 'Bộ phận: ' . auth()->user()->user->department->name . '        Chức vụ: ' . auth()->user()->user->position->name);
        $sheet->setCellValue('A11', 'Nội dung: ' . $plan->reason . '. Đề nghị hoàn ứng công tác Từ ngày: ' .
            Carbon::parse($plan->businessReport->from_date)->format('d-m-Y') . ' Đến ngày ' . Carbon::parse($plan->businessReport->to_date)->format('d-m-Y'));
        $sheet->setCellValue('C15', $total_plan);
        $sheet->setCellValue('C19', $plan->businessReport->expense);
        $sheet->setCellValue('C20', $plan->businessReport->stay);
        $sheet->setCellValue('C21', $plan->businessReport->other_costs);
        foreach ($plan->additional_costs as $e) {
            $sheet->setCellValue('B' . $row, $e->content);
            $sheet->setCellValue('C' . $row, $e->amount);
            $row++;
        }
        $sheet->setCellValue('B59', auth()->user()->name)->getStyle('C27')->getFont()->setBold(true);
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="DON_TAM_UNG.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (\Exception $exception) {
            throw $exception;
        }
    }

    public function businessReportsMobile(Request $request)
    {
        try {
            $condition = [];
            if (isset($request->status)) {
                $condition['status'] = $request->status;
            }
            $fromDate = $request->from_date;
            $data = BusinessPlan::with(['created_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }, 'confirm_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }])
                ->whereIn('status', [BusinessPlan::PLAN_APPROVED, BusinessPlan::REPORT_UPDATE, BusinessPlan::REPORT_APPROVED, BusinessPlan::REPORT_REJECTED])
                ->where($condition)
                ->where(function ($query) use ($fromDate) {
                    if ($fromDate) {
                        $formatDate = Carbon::createFromFormat('Y-m-d', $fromDate . '-01');
                        $startOfMonth = $formatDate->startOfMonth()->format('Y-m-d');
                        $endOfMonth = $formatDate->endOfMonth()->format('Y-m-d');
                        $query->where('from_date', '>=', $startOfMonth)->where('to_date', '<=', $endOfMonth)->get();
                    } else {
                        $query->get();
                    }
                })
                ->where(function ($query) use ($request) {
                    $departmentId = $request->department_id;
                    if ($departmentId) {
                        $query->whereHas('created_by.endOfWorking', function ($subQuery) use ($departmentId) {
                            return $subQuery->where('department_id', $departmentId);
                        });
                    } else {
                        $query->get();
                    }
                })->paginate(20);
            $ranks = BusinessReport::convertRank();
            return response()->json(['data' => $data, 'ranks' => $ranks]);
        } catch (\Exception $e) {
            Log::error('BusinessReportController businessReportsMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function showMobile(Request $request)
    {
        try {
            $id = $request->id;
            // check id is not null
            if (!$id) {
                return response()->json([], 400);
            }

            $data_plan = BusinessPlan::where('id', $id)->with(['plans', 'comments.created_by', 'created_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }, 'confirm_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }])->first();

            $data_report = BusinessReport::where('business_plan_id', $id)->with(['plans', 'confirm_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }, 'created_by' => function ($query) {
                $query->select('id', 'name', 'staff_code')->get();
            }])->first();

            if (!$data_report) {
                $data_report = $data_plan;
            }

            $ranks = BusinessReport::convertRank();
            $isShowBtnSave = BusinessReport::showBtnSave($data_plan);
            $disabledSelected = BusinessReport::disabledSelected($data_plan);
            return (object)['data_plan' => $data_plan, 'data_report' => $data_report, 'disabledSelected' => $disabledSelected, 'isShowBtnSave' => $isShowBtnSave, 'ranks' => $ranks];
        } catch (\Exception $e) {
            Log::error('BusinessReportController showMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function addCommentMobile(Request $request)
    {
        try {
            $validRs = $request->validate([
                'business_plan_id' => 'required',
                'comment' => 'required',
            ]);
            if (!$validRs) {
                return response()->json([], 400);
            }
            // open transaction
            DB::beginTransaction();
            $businessPlanId = $request->business_plan_id;
            $user = auth()->user();
            $content = $request->comment;
            $businessComment = new BusinessComment();
            $businessComment->business_plan_id = $businessPlanId;
            $businessComment->comment = $content;
            $businessComment->created_by = $user->id;
            $businessComment->save();
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessReportController addCommnetMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function editBusinessReportMobile(Request $request)
    {
        try {
            $user = auth()->user();

            $validate = $request->validate([
                'id' => 'required',
                'from_date' => 'required',
                'to_date' => 'required',
                'expense' => 'required',
                'stay' => 'required',
                'vehicles' => 'required',
                'staffs' => 'required',
                'plans' => 'required',
            ]);

            if (!$validate) {
                return response()->json([], 400);
            }

            $businessPlan = BusinessPlan::find($request->id);
            if (!$businessPlan) {
                return response()->json(['message' => 'Không tìm thấy dữ liệu'], 400);
            }
            $staffs = $businessPlan->staffs;

            $userId = $user->id;

            $isHasPermission = in_array($userId, array_map(function ($e) {
                return $e['id'];
            }, $staffs));
            if (!$isHasPermission) {
                return response()->json(['message' => 'Bạn không có quyền thực hiện thao tác này'], 401);
            }

            //open transaction
            DB::beginTransaction();
            $businessReport = new BusinessReport();
            $businessReport->business_plan_id = $request->id;
            $businessReport->from_date = $request->from_date;
            $businessReport->to_date = $request->to_date;
            $businessReport->expense = $request->expense;
            $businessReport->stay = $request->stay;
            $businessReport->vehicle = json_encode($request->vehicles);
            $businessReport->staffs = json_encode($request->staffs);
            $businessReport->exception = $request->exception ?? '';
            $businessPlan->other_costs = $request->other_costs;
            // update plans

            $this->updateBusinessPlans($request->id, $request->plans);

            $businessReport->save();
            BusinessPlan::where('id', $request->id)->update(['status' => BusinessPlan::REPORT_UPDATE]);
            BusinessReport::where('business_plan_id', $request->id)->update(['status' => BusinessPlan::REPORT_UPDATE]);
            DB::commit();;
            return response()->json(['message' => 'Xử lý thành công']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessReportController editBusinessReportMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    private function updateBusinessPlans($businessPlanId, $plans)
    {
        foreach ($plans as $p) {
            $convertPlan = (object)$p;
            if (isset($convertPlan->id)) {
                //check if plan_id is not of this business plan
                $businessPlanDetail = BusinessPlanDetail::where('id', $convertPlan->id)->where('business_plan_id', $businessPlanId)->first();
                if (!isset($businessPlanDetail->id)) {
                    continue;
                }
                // update business plane detail
                $businessPlanDetail->name = $convertPlan->name;
                $businessPlanDetail->content = $convertPlan->content;
                $businessPlanDetail->save();
            } else {
                // create business plane detail
                $businessPlanDetail = new BusinessPlanDetail();
                $businessPlanDetail->business_plan_id = $businessPlanId;
                $businessPlanDetail->name = $convertPlan->name;
                $businessPlanDetail->content = $convertPlan->content;
                $businessPlanDetail->save();
            }
        }
    }

    public function rankBusinessReportMobile(Request $request)
    {
        try {
            $validate = $request->validate([
                'business_plan_id' => 'required',
                'rank_id' => 'required',
            ]);
            if (!$validate) {
                return response()->json([], 400);
            }
            $user = auth()->user();
            $isLeader = UserWorking::where('user_id', $user->id)->whereHas('position', function ($query) {
                $query->where('is_leader', 1);
            })->first();

            if (!$isLeader) {
                return response()->json(['message' => 'Bạn không có quyền thực hiện thao tác này'], 401);
            }
            DB::beginTransaction();
            BusinessPlan::where('id', $request->business_plan_id)->update(['rank' => $request->rank_id]);
            BusinessReport::where('business_plan_id', $request->business_plan_id)->update(['rank' => $request->rank_id]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('BusinessReportController rankBusinessReportMobile: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
