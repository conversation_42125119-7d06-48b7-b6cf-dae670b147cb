<?php

namespace App\Http\Controllers;

use App\Builder\NotificationBuilder;
use App\Builder\NotificationConfig;
use App\Http\Requests\AbsenceLetterStoreRequest;
use App\Http\Requests\AbsenceLetterUpdateRequest;
use App\Models\AbsenceLetter;
use App\Models\AbsenceLetterType;
use App\Models\AbsenceYear;
use Illuminate\Http\Request;
use App\Models\Response;
use App\Models\User;
use App\Models\UserWorking;
use App\Models\SystemConfig;
use App\Providers\UtilityServiceProvider as u;
use Illuminate\Support\Facades\DB;
use App\Notifications\AbsenceLetterNotification;
use App\Events\ConfirmAbsenceLetterEvent;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Style;
use App\Models\BusinessTrip;
use App\Models\KPISummary;
use App\Services\FirebaseNotification;
use App\Transformer\APIJsonResponse;
use App\Transformer\AbsenceLetterTransformer;
use App\Models\Position;

class AbsenceLetterController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $userId = $user->id;
        $month = $request->month;

        $model = AbsenceLetter::with('absence_letter_type:id,name', 'user', 'user.user', 'position', 'manager:id,name')
            ->select(
                'absence_letters.*',
                DB::raw("(SELECT MAX(a.`created_at`) FROM absence_letters a WHERE a.user_id = absence_letters.user_id) check_max_created_at")
            );

        $dependentUserIds = [$userId];
        if (auth()->user()->hasPermissionTo('absence_letter_list_management', 'api')) {
            if ($request->department_id) {
                $teamUserId = UserWorking::active()
                    ->department($request->department_id)
                    ->pluck('user_id')
                    ->toArray();
                $dependentUserIds = array_merge($dependentUserIds, $teamUserId);
            }
        }

        if ($user->hasAnyRole('admin', 'ceo', 'hr_leader', 'hr')) {
            $userIds = UserWorking::allActive()
                ->department($request->department_id)
                ->position($request->department_id)
                ->pluck('user_id')
                ->toArray();

            $dependentUserIds = array_merge($dependentUserIds, $userIds);
        }

        $currentTeamUserId = [];
        if (auth()->user()->hasPermissionTo('absence_letter_approve', 'api')) {
            $currentTeamUserId = KPISummary::getAllUser(KPISummary::getAllPosition());
            $dependentUserIds = array_merge($dependentUserIds, $currentTeamUserId);
        }

        $data = $model->whereIn('user_id', $dependentUserIds)
            ->when($currentTeamUserId, function ($q, $currentTeamUserId) use ($userId) {
                $q->orWhere(function ($sq) use ($userId, $currentTeamUserId) {
                    $sq->where('manager_id', $userId)
                        ->whereNotIn('user_id', $currentTeamUserId);
                });
            })
            ->fromDate($month)
            ->orderBy('from_date', 'DESC')
            ->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    //check đơn xin nghỉ phép có đúng quy định không
    public function checkRule(Request $request)
    {
        if ($this->checkRuleStatus($request, date("Y-m-d H:i:s")) == AbsenceLetter::RULE_STATUS_FALSE) {
            return false;
        } else {
            return true;
        }
    }

    public function store(AbsenceLetterStoreRequest $request)
    {
        try {
            DB::beginTransaction();

            # không đuọc tạo nghỉ phép trong quá khứ
            // if (strtotime($request->from_date) < strtotime(date("Y-m-d"))) {
            //     return response()->json(['errors' => ['from_date' => 'Từ ngày chưa hợp lý, không được tạo ngày trong quá khứ', 'to_date' => 'Đến ngày chưa hợp lý!']], 422);
            // }

            if (strtotime($request->from_date) > strtotime($request->to_date)) {
                return response()->json(['errors' => ['from_date' => 'Từ ngày chưa hợp lý!', 'to_date' => 'Đến ngày chưa hợp lý!']], 422);
            }

            #  nghỉ sinh nhật
            if ($request->absence_letter_type_id == 2) {

                if ($request->number_days > 1) {
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Bạn chỉ được nghỉ phép sinh nhật trong 1 ngày']], 422);
                }

                if (date_format(date_create($request->from_date), 'm') != date_format(date_create($request->user()->birth_of_date), 'm')) {
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Bạn không thể nghỉ sinh nhật vào tháng này!']], 422);
                }

                $checkBirthOfDate = AbsenceLetter::where('user_id', $request->user()->id)
                    ->where('absence_letter_type_id', 2)
                    ->where('from_date', 'LIKE', date_format(date_create($request->from_date), 'Y-m') . '%')
                    ->count();
                if ($checkBirthOfDate > 0) {
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Bạn đã nghỉ sinh nhật rồi!']], 422);
                }
            }

            # check tồn tại ngày tháng giờ trước khi lưu
            $check = AbsenceLetter::where('user_id', $request->user()->id)
                // ->where('status', 2)
                ->where(function ($q) use ($request) {
                    $q->where(function ($sq) use ($request) {
                        $sq->where('from_date', '<=', $request->from_date)
                            ->where('to_date', '>=', $request->from_date);
                    })
                        ->orWhere(function ($sq) use ($request) {
                            $sq->where('from_date', '<=', $request->to_date)
                                ->where('to_date', '>=', $request->to_date);
                        });
                })
                ->count();

            $count = $check ?: 0;
            if ($count > 0) {
                return response()->json(['errors' => ['from_date' => 'Từ ngày chưa hợp lý!', 'to_date' => 'Đến ngày chưa hợp lý!']], 422);
            }

            $manager = UserWorking::allActive()
                ->where('position_id', $request->user()->user->leader->id)->with('user')
                ->first();

            if (!$manager) {
                return response()->json(['message' => 'Không tìm thấy thông tin quản lý'], 400);
            }
            $model = new AbsenceLetter();
            $model->user_id = $request->user()->id;
            $model->position_id = $request->user()->user->position->id;
            $model->manager_id = $manager->user_id;
            $model->from_date = $request->from_date;
            $model->from_time = $request->from_time;
            $model->to_date = $request->to_date;
            $model->to_time = $request->to_time;
            $model->number_days = $request->number_days;
            $model->absence_letter_type_id = $request->absence_letter_type_id;
            $model->reason = $request->reason;
            $model->rule_status = strtotime($request->from_date) < strtotime(date("Y-m-d")) ? AbsenceLetter::RULE_STATUS_FALSE : self::checkRuleStatus($request, date("Y-m-d H:i:s"));
            $model->save();

            // chỗ này phải tính toán để trừ đi ngày phép năm luôn lúc tạo bản ghi, sau này nếu từ chối ko duyệt thì hoàn lại
            if ($model->absence_letter_type_id == 1) {
                $absenceYearVal = AbsenceYear::getAbsenceYearVal($model->user_id);
                $tmpAbsenceYearVal = $absenceYearVal - $model->number_days; // tính số ngày phép còn lại sau khi nghỉ lần này
                if ($tmpAbsenceYearVal < 0) {
                    // ngày còn lại âm là có chuyện rồi
                    DB::rollback();
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Nghỉ phép năm không hợp lý!']], 422);
                }

                # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                $note = "Xin nghỉ phép {$model->number_days} ngày từ: " . vietec_format_date($model->from_date) . " đến: "  . vietec_format_date($model->to_date);
                AbsenceYear::updateAbsenceYear($model->user_id, $tmpAbsenceYearVal, $note);
            }

            # bắn notify cho người duyệt
            $confirmUserId = $model->manager_id;
            $userNoti = User::find($confirmUserId);
            $message = [
                'title' => 'Duyệt đơn xin nghỉ phép cho nhân viên: ' . $request->user()->name,
                'link' => '/absence-letters',
                'content' => 'Bạn có thông báo duyệt xin nghỉ phép cho nhân viên: ' . $request->user()->name,
                'option' => 'add',
                'type' => '2',
            ];
            $config = new NotificationConfig();
            $config->setUser($userNoti);
            $config->setMsg($message);
            $config->setNotification(function () use ($message) {
                return new AbsenceLetterNotification($message);
            });
            $config->setBroadcast(function () use ($userNoti) {
                $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\AbsenceLetterNotification")->first();
                return new ConfirmAbsenceLetterEvent($userNoti, $newMessage);
            });
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_ABSENCELETTER);

            //gửi mail cho người duyệt
            $contentMail = (object)[];
            $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký nghỉ phép của nhân sự: " . $request->user()->name;
            $contentMail->receiver = env('APP_ENV') == 'production' ? $manager['user']['email'] : '<EMAIL>';
            $contentMail->receiver_name = $manager['user']['name'];
            $contentMail->message = "Anh/chị vui lòng duyệt đơn xin nghỉ phép của nhân sự {$request->user()->name}, Loại đơn: "
                . $model->absence_letter_type->name
                . ", từ " . (($model->from_time == 1) ? '08:00' : '12:00')
                . " ngày " . vietec_format_date($model->from_date)
                . " đến " . (($model->to_time == 1) ? '12:00' : '17:00')
                . " ngày " . vietec_format_date($model->to_date);

            $contentMails = [$contentMail];

            $config->setContentMails($contentMails);

            NotificationBuilder::getBuilder()
                ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT, NotificationBuilder::EMAIL]);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $model);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController store: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    public function show($id)
    {
        $data = AbsenceLetter::where('id', $id)->with('absence_letter_type:id,name', 'user:id,name,staff_code', 'position:id,name', 'manager:id,name')->first();
        if (!$data) {
            return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
        }
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    public function update(AbsenceLetterUpdateRequest $request, $id)
    {
        try {
            $model = AbsenceLetter::where('id', $id)
                ->where('user_id', $request->user()->id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            /**Hiện tại chỉ cho update mỗi lý do nên các đoạn dưới comment lại cho nhanh */

            // # check tồn tại ngày tháng giờ trước khi lưu
            // $check = AbsenceLetter::where('user_id', $model->user_id)
            //     ->where('status', 2)
            //     ->where('id', $id)
            //     ->where(function ($q) use ($model) {
            //         $q->where(function ($sq) use ($model) {
            //             $sq->where('from_date', '<=', $model->from_date)
            //                 ->where('to_date', '>=', $model->from_date);
            //         })
            //             ->orWhere(function ($sq) use ($model) {
            //                 $sq->where('from_date', '<=', $model->to_date)
            //                     ->where('to_date', '>=', $model->to_date);
            //             });
            //     })
            //     ->count();

            // $count = $check ?: 0;

            // if ($count > 0) {
            //     return response()->json(['errors' => ['from_date' => 'Từ ngày chưa hợp lý', 'to_date' => 'Đến ngày chưa hợp lý']], 422);
            // }

            // $model->from_date = $request->from_date;
            // $model->from_time = $request->from_time;
            // $model->to_date = $request->to_date;
            // $model->to_time = $request->to_time;
            // $model->number_days = $request->number_days;
            // $model->absence_letter_type_id = $request->absence_letter_type_id;
            $model->reason = $request->reason;
            $model->save();

            return Response::formatResponse(config('apicode.SUCCESS'), $model);
        } catch (\Exception $e) {
            Log::error('AbsenceLetterController update: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    public function destroy($id)
    {
        try {

            DB::beginTransaction();

            $user_id = auth()->user()->id;
            $info = AbsenceLetter::where('id', $id)
                ->where('user_id', $user_id)
                ->first();
            if (!$info) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            # nếu là xin nghỉ phép năm thì phải hoàn lại
            if ($info->absence_letter_type_id == 1) {

                $absenceYearVal = AbsenceYear::getAbsenceYearVal($user_id);
                $tmpAbsenceYearVal = $absenceYearVal + $info->number_days;

                # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                $note = "Hoàn lại {$info->number_days} ngày phép sau khi xoá Đơn Xin nghỉ phép từ: " . vietec_format_date($info->from_date) . " đến: " . vietec_format_date($info->to_date);
                AbsenceYear::updateAbsenceYear($user_id, $tmpAbsenceYearVal, $note);
            }

            $data = AbsenceLetter::where('id', $id)->delete();
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController destroy: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    public function getManager($id)
    {
        $absence_letter_types = AbsenceLetterType::all();
        return ['absence_letter_types' => $absence_letter_types];
    }

    public function getInfo($id)
    {
        $absenceYearVal = AbsenceYear::getAbsenceYearVal($id);
        return Response::formatResponse(config('apicode.SUCCESS'), $absenceYearVal);
    }

    public function approve(Request $request, $id)
    {
        try {
            $user = $request->user();
            $absence = AbsenceLetter::where('id', $id);
            if (!$user->hasAnyRole('hr_leader', 'hr')) {
                $absence->where('manager_id', auth()->user()->id);
            }
            $absence->update(['status' => 2, 'rule_status' => $request->rule_status]);

            # bắn email cho khối nhân sự
            $model = AbsenceLetter::where('id', $id)->with('user:id,name', 'absence_letter_type:id,name')->first();
            $rs = SystemConfig::select('content')->notificationHR()->first();
            if ($rs) {
                # có cấu hình thì gửi mail
                $contentMails = [];
                $receiveEmails = explode(',', $rs->content);
                foreach ($receiveEmails as $email) {
                    $user = User::select('name')->where('email', $email)->first();
                    $contentMail = (object)[];
                    $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký nghỉ phép của nhân sự: " . $model->user->name;
                    $contentMail->receiver = $email;
                    $contentMail->receiver_name = ($user && $user->name) ? $user->name : 'Quý Anh/chị';
                    $contentMail->message = "Nhân sự {$model->user->name} đã được cấp quản lý duyệt đăng ký nghỉ phép, loại đơn: "
                        . $model->absence_letter_type->name
                        . ", từ " . (($model->from_time == 1) ? '08:00' : '12:00')
                        . " ngày " . vietec_format_date($model->from_date) . " đến "
                        . (($model->to_time == 1) ? '12:00' : '17:00') . " ngày "
                        . vietec_format_date($model->to_date) . ". Người duyệt: "
                        . auth()->user()->name . ".";
                    $contentMails[] = $contentMail;
                }
                if (!isArrEmptyOrNull($contentMails)) {
                    $config = new NotificationConfig();
                    $config->setContentMails($contentMails);
                    NotificationBuilder::getBuilder()->sendMessage($config, [NotificationBuilder::EMAIL]);
                }
            }

            return Response::formatResponse(config('apicode.SUCCESS'), true);
        } catch (\Exception $e) {
            Log::error('AbsenceLetterController approve: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    public function reject($id)
    {
        try {
            DB::beginTransaction();
            $data = AbsenceLetter::where('id', $id)
                ->where('manager_id', auth()->user()->id)
                ->update(['status' => 3]);

            // hoàn lại ngày phép năm khi bị cấp quản lý từ chối nghỉ phép
            $info = AbsenceLetter::find($id);
            if ($info->absence_letter_type_id == 1) {
                $absenceYearVal = AbsenceYear::getAbsenceYearVal($info->user_id);
                $tmpAbsenceYearVal = $absenceYearVal + $info->number_days;

                # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                $note = "Hoàn lại {$info->number_days} ngày phép sau khi bị quản lý từ chối Đơn Xin nghỉ phép từ: " . vietec_format_date($info->from_date) . " đến: "  . vietec_format_date($info->to_date);
                AbsenceYear::updateAbsenceYear($info->user_id, $tmpAbsenceYearVal, $note);
            }

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController reject: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    public function rollback($id)
    {
        try {
            DB::beginTransaction();

            $info = AbsenceLetter::where('id', $id)
                ->where('manager_id', auth()->user()->id)
                ->first();

            $rule_status = self::checkRuleStatus($info, $info->created_at);

            if ($info->absence_letter_type_id == 1) {
                # trường hợp liên quan đến nghỉ phép năm thì mới phải xem xét lại
                if ($info->status == 2) {
                    # trường hợp đã đồng ý cho nghỉ phép năm
                    $info->status = 1;
                    $info->rule_status = $rule_status;
                    $info->save();
                } else if ($info->status == 3) {
                    # trường hợp từ chối cho nghỉ phép năm
                    $absenceYearVal = AbsenceYear::getAbsenceYearVal($info->user_id);
                    $tmpAbsenceYearVal = $absenceYearVal - $info->number_days; // tính số ngày phép còn lại sau khi nghỉ lần này
                    if ($tmpAbsenceYearVal < 0) {
                        // ngày còn lại âm là có chuyện rồi
                        DB::rollback();
                        return response()->json(['errors' => ['absence_letter_type_id' => 'Nghỉ phép năm không hợp lý!']], 422);
                    }

                    # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                    $note = "[Phục hồi từ chối] Xin nghỉ phép {$info->number_days} ngày từ: " . vietec_format_date($info->from_date) . " đến: "  . vietec_format_date($info->to_date);
                    AbsenceYear::updateAbsenceYear($info->user_id, $tmpAbsenceYearVal, $note);

                    $info->status = 1;
                    $info->rule_status = $rule_status;
                    $info->save();
                }
            } else {
                # trường hợp không liên quan phép năm thì chỉ cần đổi status là được
                $info->status = 1;
                $info->rule_status = $rule_status;
                $info->save();
            }

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), true);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController rollback: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    # hàm thêm mới cho toàn bộ công ty 1 ngày phép
    # Dành cho nhân sự thêm các ngày mà cty cho nghỉ toàn công ty
    public function addAllAbsence(Request $request)
    {
        try {
            DB::beginTransaction();

            $params = (object) $request->all();
            $data = [
                'from_date' => $params->from_date,
                'to_date' => $params->from_date,
                'from_time' => 1,
                'to_time' => 2,
                'reason' => $params->reason,
                'absence_letter_type_id' => 3,
                'status' => 2,
                'number_days' => 1
            ];

            $userLists = UserWorking::notAdmin()->active()->with('user')->groupBy('user_id')->get();
            foreach ($userLists as $user) {

                if (!$user->user) {
                    continue;
                }

                # lấy Manager của user
                $managerId = u::getManager($user->user_id, false);

                $fieldData = $data;
                $fieldData['user_id'] = $user->user_id;
                $fieldData['position_id'] = $user->position_id;
                $fieldData['manager_id'] = $managerId;

                AbsenceLetter::create($fieldData);
            }

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), true);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController addAllAbsence: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý!'], 500);
        }
    }

    private static function checkRuleStatus($params, $date)
    {
        $resp = AbsenceLetter::RULE_STATUS_TRUE;
        $date = $date ?: date("Y-m-d H:i:s");
        $fromTime = $params->from_time;
        $fromDate = $params->from_date;
        $numberDays = $params->number_days;

        $fullFromDateTime = $fromDate . ' ' . AbsenceLetter::FROM_TIME_OPTION[$fromTime] . ':00';
        $diff = Carbon::create($date)->diffInHours(Carbon::create($fullFromDateTime));

        if ($numberDays <= 1 && $diff < 12) {

            // <= 1 ngày thì phải xin phép trước ít nhất 12 giờ
            $resp = AbsenceLetter::RULE_STATUS_FALSE;
        } else if ($numberDays > 1 && $numberDays < 4 && $diff < 48) {

            // > 1 và < 4 ngày thì phải xin phép trước ít nhất 2 ngày: 2x24 = 48 h
            $resp = AbsenceLetter::RULE_STATUS_FALSE;
        } else if ($numberDays >= 4 && $numberDays < 7 && $diff < 168) {

            // >= 4 và < 7 ngày thì phải xin phép trước ít nhất 7 ngày: 7x24 = 168 h
            $resp = AbsenceLetter::RULE_STATUS_FALSE;
        } else if ($numberDays >= 7 && $diff < 336) {

            // >= 7 ngày thì phải xin phép trước ít nhất 2 tuần: 14x24 = 336 h
            $resp = AbsenceLetter::RULE_STATUS_FALSE;
        }
        return $resp;
    }

    public function exportExcel(Request $request)
    {
        $inputFileName = 'templates/absence_letters.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $datas = AbsenceLetter::with('absence_letter_type:id,name', 'user', 'manager:id,name')
            ->fromDate($request->month)
            ->orderBy('from_date', 'DESC')
            ->get();

        $j = 5;
        foreach ($datas as $k => $item) {
            $sheet->setCellValue("A" . $j, ($k + 1));
            $sheet->setCellValue("B" . $j, $item['user']['name'] ?: '');
            $sheet->setCellValue("C" . $j, $item['user']['staff_code'] ?: '');
            $sheet->setCellValue("D" . $j, $item['manager'] ? $item['manager']['name'] : '');
            $sheet->setCellValue("E" . $j, date_format(date_create($item['from_date']), 'd/m/Y') . AbsenceLetter::FROM_TIME_OPTION[$item['from_time']]);
            $sheet->setCellValue("F" . $j, date_format(date_create($item['to_date']), 'd/m/Y') . AbsenceLetter::TO_TIME_OPTION[$item['to_time']]);
            $sheet->setCellValue("G" . $j, $item['number_days']);
            $sheet->setCellValue("H" . $j, $item['absence_letter_type']['name'] ?: '');
            $sheet->setCellValue("I" . $j, $item['reason']);
            $sheet->setCellValue("J" . $j, AbsenceLetter::STATUS[$item['status']]);
            $j++;
        }

        $sheet->getStyle('A5:J' . ($j - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="danh_sach_xin_nghi_phep.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    public function explain(AbsenceLetterStoreRequest $request)
    {
        try {
            DB::beginTransaction();

            #  nghỉ sinh nhật
            if ($request->absence_letter_type_id == 2) {

                if ($request->number_days > 1) {
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Bạn chỉ được nghỉ phép sinh nhật trong 1 ngày']], 422);
                }

                if (date_format(date_create($request->from_date), 'm') != date_format(date_create($request->user()->birth_of_date), 'm')) {
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Bạn không thể nghỉ sinh nhật vào tháng này!']], 422);
                }

                $checkBirthOfDate = AbsenceLetter::where('user_id', $request->user['id'])
                    ->where('absence_letter_type_id', 2)
                    ->where('from_date', 'LIKE', date_format(date_create($request->from_date), 'Y-m') . '%')
                    ->count();
                if ($checkBirthOfDate > 0) {
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Bạn đã nghỉ sinh nhật rồi!']], 422);
                }
            }

            # check tồn tại ngày tháng giờ trước khi lưu
            $check = AbsenceLetter::where('user_id', $request->user['id'])
                ->where('status', 2)
                ->where(function ($q) use ($request) {
                    $q->where(function ($sq) use ($request) {
                        $sq->where('from_date', '<=', $request->from_date)
                            ->where('to_date', '>=', $request->from_date);
                    })
                        ->orWhere(function ($sq) use ($request) {
                            $sq->where('from_date', '<=', $request->to_date)
                                ->where('to_date', '>=', $request->to_date);
                        });
                })
                ->count();

            $count = $check ?: 0;
            if ($count > 0) {
                return response()->json(['errors' => ['from_date' => 'Từ ngày chưa hợp lý!', 'to_date' => 'Đến ngày chưa hợp lý!']], 422);
            }
            $staff = UserWorking::active()->where('user_id', $request->user['id'])->first();
            $manager = UserWorking::allActive()
                ->where('position_id', $staff->dependent_position_id)
                ->first();

            if (!$manager) {
                return response()->json(['message' => 'Không tìm thấy thông tin quản lý'], 400);
            }
            $model = new AbsenceLetter();
            $model->user_id = $request->user['id'];
            $model->position_id = $staff->position_id;
            $model->manager_id = $manager->user_id;
            $model->from_date = $request->from_date;
            $model->from_time = $request->from_time;
            $model->to_date = $request->to_date;
            $model->to_time = $request->to_time;
            $model->number_days = $request->number_days;
            $model->absence_letter_type_id = $request->absence_letter_type_id;
            $model->reason = $request->reason;
            $model->status = BusinessTrip::CONFIRMED;
            $model->rule_status = self::checkRuleStatus($request, date("Y-m-d H:i:s"));
            $model->save();

            // chỗ này phải tính toán để trừ đi ngày phép năm luôn lúc tạo bản ghi, sau này nếu từ chối ko duyệt thì hoàn lại
            if ($model->absence_letter_type_id == 1) {
                $absenceYearVal = AbsenceYear::getAbsenceYearVal($model->user_id);
                $tmpAbsenceYearVal = $absenceYearVal - $model->number_days; // tính số ngày phép còn lại sau khi nghỉ lần này
                if ($tmpAbsenceYearVal < 0) {
                    // ngày còn lại âm là có chuyện rồi
                    DB::rollback();
                    return response()->json(['errors' => ['absence_letter_type_id' => 'Nghỉ phép năm không hợp lý!']], 422);
                }

                # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                $note = "Xin nghỉ phép {$model->number_days} ngày từ: " . vietec_format_date($model->from_date) . " đến: "  . vietec_format_date($model->to_date);
                AbsenceYear::updateAbsenceYear($model->user_id, $tmpAbsenceYearVal, $note);
            }

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $model);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController store: ' . $e->getMessage());
            return response()->json(['message' => 'Xảy ra lỗi trong quá trình xử lý'], 500);
        }
    }

    public function listAbsenceLetter(Request $request)
    {
        try {
            $user = $request->user();
            $userId = $user->id;
            $month = $request->month;

            $model = AbsenceLetter::with('absence_letter_type:id,name', 'user', 'user.user', 'position', 'manager:id,name')
                ->select(
                    'absence_letters.*',
                    DB::raw("(SELECT MAX(a.`created_at`) FROM absence_letters a WHERE a.user_id = absence_letters.user_id) check_max_created_at")
                );

            $dependentUserIds = [$userId];
            if (auth()->user()->hasPermissionTo('absence_letter_list_management', 'api')) {
                if ($request->department_id) {
                    $teamUserId = UserWorking::active()
                        ->department($request->department_id)
                        ->pluck('user_id')
                        ->toArray();
                    $dependentUserIds = array_merge($dependentUserIds, $teamUserId);
                }
            }

            if ($user->hasAnyRole('admin', 'hr_lear', 'hr')) {
                $userIds = UserWorking::allActive()
                    ->department($request->department_id)
                    ->position($request->department_id)
                    ->pluck('user_id')
                    ->toArray();

                $dependentUserIds = array_merge($dependentUserIds, $userIds);
            }

            $currentTeamUserId = [];
            if (auth()->user()->hasPermissionTo('absence_letter_approve', 'api')) {
                $currentTeamUserId = KPISummary::getAllUser(KPISummary::getAllPosition());
                $dependentUserIds = array_merge($dependentUserIds, $currentTeamUserId);
            }

            $data = $model->whereIn('user_id', $dependentUserIds)
                ->when($currentTeamUserId, function ($q, $currentTeamUserId) use ($userId) {
                    $q->orWhere(function ($sq) use ($userId, $currentTeamUserId) {
                        $sq->where('manager_id', $userId)
                            ->whereNotIn('user_id', $currentTeamUserId);
                    });
                })
                ->fromDate($month)
                ->orderBy('from_date', 'DESC')
                ->get();

            $rs = (new APIJsonResponse)->responseSuccess((new AbsenceLetterTransformer)->transforms($data));
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function viewAddMobile(Request $request)
    {
        try {
            $user = auth()->user();
            $staff = UserWorking::active()->where('user_id', $user->id)->first();

            $manager = UserWorking::allActive()
                ->where('position_id', $request->user()->user->leader->id)->with('user')
                ->first();

            if (!$manager) {
                return response()->json(['description' => 'Không tìm thấy thông tin quản lý'], 400);
            }
            $absenceYearVal = AbsenceYear::getAbsenceYearVal($user->id);
            $position = Position::year(date('Y'))->select('id', 'name', 'is_leader', 'department_id')->whereId($staff->position_id)->first();

            $arr_view_data = [
                "user_id"                   => $user->id,
                "staff_code"                => $user->staff_code,
                "user_name"                 => $user->name,
                "position_id"               => $staff->position_id,
                "position"                  => $position->name,
                //"from_date"                 => $request->from_date,
                //"from_time"                 => $request->from_time,
                //"to_time"                   => $request->to_time,
                //"to_date"                   => $request->to_date,
                //"number_days"               => $request->number_days,
                //"absence_letter_type_id"    => $request->absence_letter_type_id,
                //"reason"                    => $request->reason,
                "status"                    => 1,
                "manager_id"                => $manager->user_id,
                "absence_year_val"          => $absenceYearVal
            ];

            $rs = (new APIJsonResponse)->responseSuccess($arr_view_data);
        } catch (\Exception $e) {
            Log::error('AbsenceLetterController destroy: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function addMobile(AbsenceLetterStoreRequest $request)
    {
        try {
            // $add = [
            //     "user_id"                   => 6,
            //     "staff_code"                => "NV008",
            //     "user_name"                 => "Nguyễn Nhã Huy",
            //     "position_id"               => 27,
            //     "position"                  => "Nhân viên phát triển sản phẩm",
            //     "from_date"                 => "2024-04-16",
            //     "from_time"                 => "1",
            //     "to_time"                   => "2",
            //     "to_date"                   => "2024-04-16",
            //     "number_days"               => 1,
            //     "absence_letter_type_id"    => 1,
            //     "reason"                    => "Xin nghỉ có việc",
            //     "status"                    => 1,
            //     "employment_contract"       => 5,
            //     "absence_year_val"          => 4.5
            // ];

            // $request = (Object)$add;

            DB::beginTransaction();

            # không đuọc tạo nghỉ phép trong quá khứ
            if (strtotime($request->from_date) < strtotime(date("Y-m-d"))) {
                return response()->json(['description' => 'Từ ngày chưa hợp lý, không được tạo ngày trong quá khứ'], 422);
            }

            if (strtotime($request->from_date) > strtotime($request->to_date)) {
                return response()->json(['description' => 'Ngày nghỉ chưa hợp lý!'], 422);
            }

            #  nghỉ sinh nhật
            if ($request->absence_letter_type_id == 2) {

                if ($request->number_days > 1) {
                    return response()->json(['description' => 'Bạn chỉ được nghỉ phép sinh nhật trong 1 ngày'], 422);
                }

                if (date_format(date_create($request->from_date), 'm') != date_format(date_create($request->user()->birth_of_date), 'm')) {
                    return response()->json(['description' => 'Bạn không thể nghỉ sinh nhật vào tháng này!'], 422);
                }

                $checkBirthOfDate = AbsenceLetter::where('user_id', $request->user_id)
                    ->where('absence_letter_type_id', 2)
                    ->where('from_date', 'LIKE', date_format(date_create($request->from_date), 'Y-m') . '%')
                    ->count();
                if ($checkBirthOfDate > 0) {
                    return response()->json(['description' => 'Bạn đã nghỉ sinh nhật rồi!'], 422);
                }
            }

            # check tồn tại ngày tháng giờ trước khi lưu
            $check = AbsenceLetter::where('user_id', $request->user_id)
                ->where('status', 2)
                ->where(function ($q) use ($request) {
                    $q->where(function ($sq) use ($request) {
                        $sq->where('from_date', '<=', $request->from_date)
                            ->where('to_date', '>=', $request->from_date);
                    })
                        ->orWhere(function ($sq) use ($request) {
                            $sq->where('from_date', '<=', $request->to_date)
                                ->where('to_date', '>=', $request->to_date);
                        });
                })
                ->count();

            $count = $check ?: 0;
            if ($count > 0) {
                return response()->json(['description' => 'Ngày nghỉ chưa hợp lý!'], 422);
            }

            $manager = UserWorking::allActive()
                ->where('position_id', $request->user()->user->leader->id)->with('user')
                ->first();

            if (!$manager) {
                return response()->json(['message' => 'Không tìm thấy thông tin quản lý'], 400);
            }
            $model = new AbsenceLetter();
            $model->user_id = $request->user_id;
            $model->position_id = $request->position_id;
            $model->manager_id = $manager->user_id;
            $model->from_date = $request->from_date;
            $model->from_time = $request->from_time;
            $model->to_date = $request->to_date;
            $model->to_time = $request->to_time;
            $model->number_days = $request->number_days;
            $model->absence_letter_type_id = $request->absence_letter_type_id;
            $model->reason = $request->reason;
            $model->rule_status = self::checkRuleStatus($request, date("Y-m-d H:i:s"));
            $model->save();

            // chỗ này phải tính toán để trừ đi ngày phép năm luôn lúc tạo bản ghi, sau này nếu từ chối ko duyệt thì hoàn lại
            if ($model->absence_letter_type_id == 1) {
                $absenceYearVal = AbsenceYear::getAbsenceYearVal($model->user_id);
                $tmpAbsenceYearVal = $absenceYearVal - $model->number_days; // tính số ngày phép còn lại sau khi nghỉ lần này
                if ($tmpAbsenceYearVal < 0) {
                    // ngày còn lại âm là có chuyện rồi
                    DB::rollback();
                    return response()->json(['description' => 'Nghỉ phép năm không hợp lý!'], 422);
                }

                # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                $note = "Xin nghỉ phép {$model->number_days} ngày từ: " . vietec_format_date($model->from_date) . " đến: "  . vietec_format_date($model->to_date);
                AbsenceYear::updateAbsenceYear($model->user_id, $tmpAbsenceYearVal, $note);
            }

            DB::commit();

            # bắn notify cho người duyệt
            $confirmUserId = $model->manager_id;
            $userNoti = User::find($confirmUserId);
            $message = [
                'title' => 'Duyệt đơn xin nghỉ phép cho nhân viên: ' . $request->user_name,
                'link' => '/absence-letters',
                'content' => 'Bạn có thông báo duyệt xin nghỉ phép cho nhân viên: ' . $request->user_name,
                'option' => 'add',
                'type' => '2',
            ];

            $config = new NotificationConfig();
            $config->setMsg($message);
            $config->setUser($userNoti);
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_ABSENCELETTER);
            $config->setNotification(function () use ($message) {
                return new AbsenceLetterNotification($message);
            });

            $config->setBroadcast(function () use ($userNoti) {
                $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\AbsenceLetterNotification")->first();
                return new ConfirmAbsenceLetterEvent($userNoti, $newMessage);
            });

            //gửi mail cho người duyệt
            $contentMail = (object)[];
            $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký nghỉ phép của nhân sự: " . $request->user_name;
            $contentMail->receiver = env('APP_ENV') == 'local' ? $manager['user']['email'] : '<EMAIL>';
            $contentMail->receiver_name = $manager['user']['name'];
            $contentMail->message = "Anh/chị vui lòng duyệt đơn xin nghỉ phép của nhân sự {$request->user_name}, Loại đơn: " . $model->absence_letter_type->name . ", từ " . (($model->from_time == 1) ? '08:00' : '12:00') . " ngày " . vietec_format_date($model->from_date) . " đến " . (($model->to_time == 1) ? '12:00' : '17:00') . " ngày " . vietec_format_date($model->to_date);
            $config->setContentMails([$contentMail]);

            NotificationBuilder::getBuilder()->sendMessage($config, [
                NotificationBuilder::MOBILE,
                NotificationBuilder::EVENT,
                NotificationBuilder::EMAIL
            ]);

            $rs = (new APIJsonResponse)->responseSuccess($model);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController store: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }
        return $rs;
    }

    public function viewUpdateMobile(Request $request)
    {
        try {
            $id = $request->id;
            $data = AbsenceLetter::where('id', $id)->with('absence_letter_type:id,name', 'user:id,name,staff_code', 'position:id,name', 'manager:id,name')->first();
            if (!$data) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            $rs = (new APIJsonResponse)->responseSuccess((new AbsenceLetterTransformer)->viewUpdateTransform($data));
        } catch (\Exception $e) {
            Log::error('AbsenceLetterController destroy: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }
        return $rs;
    }

    public function updateMobile(Request $request)
    {
        try {
            $id = $request->id;
            $model = AbsenceLetter::where('id', $id)
                ->where('user_id', $request->user()->id)
                ->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            $model->reason = $request->reason;
            $model->save();

            $rs = (new APIJsonResponse)->responseSuccess($model);
        } catch (\Exception $e) {
            Log::error('AbsenceLetterController update: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }
        return $rs;
    }

    public function getManagerMobile()
    {
        try {
            $absence_letter_types = AbsenceLetterType::all();

            $rs = (new APIJsonResponse)->responseSuccess((new AbsenceLetterTransformer)->transforms($absence_letter_types, 1));
        } catch (Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function destroyMobile(Request $request)
    {
        try {
            $id = $request->id;
            DB::beginTransaction();

            $user_id = auth()->user()->id;
            $info = AbsenceLetter::where('id', $id)
                ->where('user_id', $user_id)
                ->first();
            if (!$info) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            # nếu là xin nghỉ phép năm thì phải hoàn lại
            if ($info->absence_letter_type_id == 1) {

                $absenceYearVal = AbsenceYear::getAbsenceYearVal($user_id);
                $tmpAbsenceYearVal = $absenceYearVal + $info->number_days;

                # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                $note = "Hoàn lại {$info->number_days} ngày phép sau khi xoá Đơn Xin nghỉ phép từ: " . vietec_format_date($info->from_date) . " đến: "  . vietec_format_date($info->to_date);
                AbsenceYear::updateAbsenceYear($user_id, $tmpAbsenceYearVal, $note);
            }

            $data = AbsenceLetter::where('id', $id)->delete();
            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController destroy: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function viewApprove(Request $request)
    {
        try {
            $id = $request->id;
            $model = AbsenceLetter::where('id', $id)->first();
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 400);
            }

            $rs = (new APIJsonResponse)->responseSuccess((new AbsenceLetterTransformer)->approveTransform($model));
        } catch (\Exception $e) {
            Log::error('AbsenceLetterController update: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }
        return $rs;
    }

    public function approveMobile(Request $request)
    {
        try {
            $id = $request->id;
            $rule_status = $request->rule_status ?: 1;

            $user = $request->user();
            $absence = AbsenceLetter::where('id', $id);
            if (!$user->hasAnyRole('hr_leader', 'hr')) {
                $absence->where('manager_id', auth()->user()->id);
            }
            $absence->update(['status' => 2, 'rule_status' => $rule_status]);

            # bắn email cho khối nhân sự
            $model = AbsenceLetter::where('id', $id)->with('user:id,name', 'absence_letter_type:id,name')->first();
            $rs = SystemConfig::select('content')->notificationHR()->first();
            if ($rs) {
                # có cấu hình thì gửi mail
                $receiveEmails = explode(',', $rs->content);
                $contentMails = [];
                foreach ($receiveEmails as $email) {
                    $user = User::select('name')->where('email', $email)->first();
                    $contentMail = (object)[];
                    $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký nghỉ phép của nhân sự: " . $model->user->name;
                    $contentMail->receiver = $email;
                    $contentMail->receiver_name = ($user && $user->name) ? $user->name : 'Quý Anh/chị';
                    $contentMail->message = "Nhân sự {$model->user->name} đã được cấp quản lý duyệt đăng ký nghỉ phép, loại đơn: " . $model->absence_letter_type->name . ", từ " . (($model->from_time == 1) ? '08:00' : '12:00') . " ngày " . vietec_format_date($model->from_date) . " đến " . (($model->to_time == 1) ? '12:00' : '17:00') . " ngày " . vietec_format_date($model->to_date) . ". Người duyệt: " . auth()->user()->name . ".";
                    $contentMails[] = $contentMail;
                }
                if (!isArrEmptyOrNull($contentMails)) {
                    $config = new NotificationConfig();
                    $config->setContentMails($contentMails);
                    NotificationBuilder::getBuilder()->sendMessage($config, [NotificationBuilder::EMAIL]);
                }
            }

            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            Log::error('AbsenceLetterController approve: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }
        return $rs;
    }

    public function rejectMobile(Request $request)
    {
        try {
            $id = $request->id;
            DB::beginTransaction();
            $data = AbsenceLetter::where('id', $id)
                ->where('manager_id', auth()->user()->id)->first();

            if ($data->status == "2") {
                return response()->json(['description' => 'Thao tác này lỗi ! Đơn đã được duyệt rồi !'], 400);
            } else {
                $data->update(['status' => 3]);
            }

            // hoàn lại ngày phép năm khi bị cấp quản lý từ chối nghỉ phép
            $info = AbsenceLetter::find($id);
            if ($info->absence_letter_type_id == 1) {
                $absenceYearVal = AbsenceYear::getAbsenceYearVal($info->user_id);
                $tmpAbsenceYearVal = $absenceYearVal + $info->number_days;

                # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                $note = "Hoàn lại {$info->number_days} ngày phép sau khi bị quản lý từ chối Đơn Xin nghỉ phép từ: " . vietec_format_date($info->from_date) . " đến: "  . vietec_format_date($info->to_date);
                AbsenceYear::updateAbsenceYear($info->user_id, $tmpAbsenceYearVal, $note);
            }

            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess($data);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController reject: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function rollbackMobile(Request $request)
    {
        try {
            $id = $request->id;
            DB::beginTransaction();

            $info = AbsenceLetter::where('id', $id)
                ->where('manager_id', auth()->user()->id)
                ->first();

            $rule_status = self::checkRuleStatus($info, $info->created_at);

            if ($info->absence_letter_type_id == 1) {
                # trường hợp liên quan đến nghỉ phép năm thì mới phải xem xét lại
                if ($info->status == 2) {
                    # trường hợp đã đồng ý cho nghỉ phép năm
                    $info->status = 1;
                    $info->rule_status = $rule_status;
                    $info->save();
                } else if ($info->status == 3) {
                    # trường hợp từ chối cho nghỉ phép năm
                    $absenceYearVal = AbsenceYear::getAbsenceYearVal($info->user_id);
                    $tmpAbsenceYearVal = $absenceYearVal - $info->number_days; // tính số ngày phép còn lại sau khi nghỉ lần này
                    if ($tmpAbsenceYearVal < 0) {
                        // ngày còn lại âm là có chuyện rồi
                        DB::rollback();
                        return response()->json(['errors' => ['absence_letter_type_id' => 'Nghỉ phép năm không hợp lý!']], 422);
                    }

                    # mỗi lần cập nhật bản ghi của danh phần quản lý nghỉ phép đều phải qua hàm này để ghi log
                    $note = "[Phục hồi từ chối] Xin nghỉ phép {$info->number_days} ngày từ: " . vietec_format_date($info->from_date) . " đến: "  . vietec_format_date($info->to_date);
                    AbsenceYear::updateAbsenceYear($info->user_id, $tmpAbsenceYearVal, $note);

                    $info->status = 1;
                    $info->rule_status = $rule_status;
                    $info->save();
                }
            } else {
                # trường hợp không liên quan phép năm thì chỉ cần đổi status là được
                $info->status = 1;
                $info->rule_status = $rule_status;
                $info->save();
            }

            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController rollback: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }
}
