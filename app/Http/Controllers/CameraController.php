<?php

namespace App\Http\Controllers;

use App\Models\PersonInOut;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CameraController extends Controller
{

    public function syncDataCamAI(Request $request)
    {
        DB::transaction(function () use (&$request) {
            $row = new PersonInOut();
            foreach ($request->all() as $field => $value) {
                if (!in_array($field, ['temp', 'id', 'data'])) {
                    $row->$field = $value;
                }
            }
            $row->save();
        });
        return response()->json(['success' => 'success'], 200);
    }
}
