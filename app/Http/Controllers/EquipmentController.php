<?php

namespace App\Http\Controllers;

use App\Http\Requests\EquipmentStoreRequest;
use App\Http\Requests\EquipmentUpdateRequest;
use App\Http\Requests\ImportEquipmentRequest;
use App\Models\Equipment;
use App\Models\EquipmentType;
use App\Models\ImportWarehouseHistory;
use App\Models\Response;
use App\Models\SubEquipment;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class EquipmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = ImportWarehouseHistory::with([
            'warehouse',
            'subEquipments',
            'equipment.equipment_type'
        ])
            ->orderBy('created_at', 'DESC')
            ->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(EquipmentStoreRequest $request)
    {
        try {
            if (!!$request->require_code && count($request->sub_equipments) != +$request['data']['amount']) {
                return response()->json(['message' => 'Số lượng hàng hóa và số mã không bằng nhau'], 400);
            }

            if (SubEquipment::whereIn('code', $request->sub_equipments)
                ->first()
            ) {
                return response()->json(['message' => 'Có ít nhất một mã hàng hóa đã tồn tại!'], 400);
            }

            $eq = Equipment::where('warehouse_id', $request['data']['warehouse_id'])
                ->where('equipment_type_id', $request['data']['equipment_type_id'])
                ->first();
            DB::beginTransaction();
            $eId = null;
            if ($eq) {
                $data = Equipment::where('id', $eq->id)
                    ->update([
                        'amount' => $eq->amount + $request['data']['amount']
                    ]);
                $eId = $eq->id;
            } else {
                $data = $request->data;
                $equipment = Equipment::create($data);
                $eId = $equipment->id;
            }

            // save histoy
            $importWarehouse = new ImportWarehouseHistory();
            $importWarehouse->warehouse_id = $request->data['warehouse_id'];
            $importWarehouse->equipment_id = $eId;
            $importWarehouse->amount = $request->data['amount'];
            $importWarehouse->price = $request->data['price'];
            $importWarehouse->created_by = $request->user()->id;
            $importWarehouse->save();

            $newSubs = [];
            foreach ($request->sub_equipments as $code) {
                $newSubs[] = [
                    'equipment_id' => $eId,
                    'import_warehouse_history_id' => $importWarehouse->id,
                    'code' => $code,
                    'is_used' => false,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ];
            }

            if ($newSubs) {
                DB::table('sub_equipments')->insert($newSubs);
            }
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $data);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error("EquipmentController store: " . $e->getMessage());
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     * logic đang bị sai
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Equipment $Equipment
     * @return \Illuminate\Http\Response
     */
    public function update(EquipmentUpdateRequest $request, $id)
    {
        if (!!$request->require_code && count($request->sub_equipments) != +$request->data['amount']) {
            return response()->json(['message' => 'Số lượng hàng hóa và số mã không bằng nhau'], 400);
        }

        if (SubEquipment::whereIn('code', $request->sub_equipments)
            ->where('import_warehouse_history_id', '<>', $id)
            ->first()
        ) {
            return response()->json(['message' => 'Có ít nhất một mã hàng hóa đã tồn tại!'], 400);
        }

        try {
            DB::beginTransaction();
            $model = ImportWarehouseHistory::find($id);
            if (!$model) {
                return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
            }

            $amount = !!$request->require_code ? count($request->sub_equipments) : $request->data['amount'];
            $model->amount = $amount;
            $model->price = $request->data['price'];
            $model->save();

            SubEquipment::whereNotIn('code', $request->sub_equipments)
                ->where('equipment_id', $model->equipment_id)
                ->where('import_warehouse_history_id', $id)
                ->where('is_used', false)
                ->delete();
            $codeExists = SubEquipment::where('equipment_id', $model->equipment_id)
                ->where('import_warehouse_history_id', $id)
                ->pluck('code')
                ->toArray();
            $newCodes = array_diff($request->sub_equipments, $codeExists);
            Equipment::where('id', $model->equipment_id)
                ->update([
                    'amount' => DB::raw('amount + ' . count($newCodes))
                ]);
            $newSubs = [];
            foreach ($newCodes as $code) {
                $newSubs[] = [
                    'equipment_id' => $model->equipment_id,
                    'import_warehouse_history_id' => $model->id,
                    'code' => $code,
                    'is_used' => false,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ];
            }

            if ($newSubs) {
                DB::table('sub_equipments')->insert($newSubs);
            }

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $request->sub_equipments);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error("EquipmentController update: " . $e->getMessage());
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Equipment $Equipment
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $model = ImportWarehouseHistory::find($id);
        if (!$model) {
            return response()->json(['message' => 'Dữ liệu không tồn tại'], 404);
        }

        $model->equipment()->update(['amount' => DB::raw('amount - ' . $model->amount)]);

        if ($model->subEquipments()) {
            $model->subEquipments()->delete();
        }
        $model->delete();

        return Response::formatResponse(config('apicode.SUCCESS'), []);
    }

    public function downloadFileTemplate()
    {
        $file = public_path() . '/storage/file/import_warehouse.xlsx';
        return response()->download($file);
    }

    public function importExcel(ImportEquipmentRequest $request)
    {
        try {
            DB::beginTransaction();
            $warehouse_id = $request->warehouse_id;
            $array_convert = vietec_convert_array_to_map($request->data, 'equipment_code', true);
            $history = [];
            foreach ($array_convert as $array_key) {
                foreach ($array_key as $el) {
                    $equipment_type = EquipmentType::where('code', $el['equipment_code'])->first();

                    if (!$equipment_type) continue;

                    $equipment = Equipment::where('warehouse_id', $warehouse_id)->where('equipment_type_id', $equipment_type->id)->first();

                    if (isset($equipment)) {
                        $amountInput = $el['sub_equipment'] == "" ? +$el['amount'] : 1;
                        $amount = $el['sub_equipment'] == "" ? +$el['amount'] + $equipment->amount : $equipment->amount + 1;
                        Equipment::where('id', $equipment->id)->update(['amount' => $amount]);
                        $equipment_id = $equipment->id;
                        // save histoy
                        $history[] = [
                            'id' => Str::uuid()->toString(),
                            'warehouse_id' => $warehouse_id,
                            'equipment_id' => $equipment->id,
                            'amount' => $amountInput,
                            'price' => $el['price'],
                            'created_by' => $request->user()->id,
                            'created_at' => date("Y-m-d H:i:s"),
                            'updated_at' => date("Y-m-d H:i:s"),
                        ];
                    } else {
                        $data = Equipment::create([
                            'equipment_type_id' => $equipment_type->id,
                            'warehouse_id' => $warehouse_id,
                            'amount' => $el['amount']
                        ]);
                        $equipment_id = $data->id;
                        $history[] = [
                            'id' => Str::uuid()->toString(),
                            'warehouse_id' => $warehouse_id,
                            'equipment_id' => $equipment_id,
                            'amount' => $el['amount'],
                            'price' => $el['price'],
                            'created_by' => $request->user()->id,
                            'created_at' => date("Y-m-d H:i:s"),
                            'updated_at' => date("Y-m-d H:i:s"),
                        ];
                    }
                    if ($el['sub_equipment']) {
                        SubEquipment::create([
                            'equipment_id' => $equipment_id,
                            'code' => $el['sub_equipment'],
                        ]);
                    }
                }
            }

            if ($history) {
                DB::table('import_warehouse_history')->insert($history);
            }

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), 1);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error("EquipmentController update: " . $e->getMessage());
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }
}
