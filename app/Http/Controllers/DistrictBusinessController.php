<?php

namespace App\Http\Controllers;

use App\Models\DistrictBusinessMarket;
use Illuminate\Http\Request;
use App\Models\Response;

class DistrictBusinessController extends Controller
{
    public function index(Request $request)
    {
        $province_id = empty($request->province_id) ? 0 : $request->province_id;
        $data = DistrictBusinessMarket::where('province_id', $province_id)->get();
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
