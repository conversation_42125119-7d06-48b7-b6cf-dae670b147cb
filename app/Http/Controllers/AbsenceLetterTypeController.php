<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AbsenceLetterType;
use App\Models\Response;

class AbsenceLetterTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = AbsenceLetterType::paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }
}
