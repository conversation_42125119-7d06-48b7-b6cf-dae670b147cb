<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Report;
use App\Providers\UtilityServiceProvider as u;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Style;
use App\Models\Position;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\QuarterlyGoals;

class ExcelController extends Controller
{
    /**
     * Hàm test khởi tạo excel
     */
    public function test_export_excel(Request $request)
    {

        # phần này là tự động tạo file
        // $spreadsheet = new Spreadsheet();
        // $sheet = $spreadsheet->getActiveSheet();
        // $sheet->setCellValue('A1', 'VIETEC CORP,.');
        // $sheet->setCellValue('A5', 'Annp test api export excel');

        // $writer = new Xlsx($spreadsheet);
        // try {
        //     header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        //     header('Content-Disposition: attachment;filename="test.xlsx"');
        //     header('Cache-Control: max-age=0');
        //     $writer->save("php://output");
        // } catch (Exception $exception) {
        //     throw $exception;
        // }

        # phần này là đọc từ 1 file teaplate đã định dạng đẹp từ trước
        $inputFileName = 'templates/test.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $sheet->setCellValue('A1', 'VIETEC CORP,.');
        $sheet->setCellValue('A5', 'Annp test api export excel');

        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="test.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Xuất excel Báo cáo doanh số tuần - theo tỉnh thành
     */
    public function report_sale_week(Request $request)
    {
        $inputFileName = 'templates/report_sale_week.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $p = Report::params($request);
        $data = Report::report_sale_week($p);

        $sheet->setCellValue("A3", "Từ ngày: " . $p->f . " đến ngày: " . $p->t);

        $row = 6;
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item->province_name);
            $sheet->setCellValue("C" . $row, $item->total_contracts);
            $sheet->setCellValue("D" . $row, vietec_format_number($item->total_amounts, true));
            $row++;
        }
        $sheet->getStyle('A5:D' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="report_sale_week.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Xuất excel Báo cáo doanh số theo sản phẩm
     */
    public function report_sale_product(Request $request)
    {
        $inputFileName = 'templates/report_sale_product.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $p = Report::params($request);
        $data = Report::report_sale_product($p);
        $products = ProductCategory::orderBy('code')->pluck('code')->toArray();
        $sheet->setCellValue("A3", "Từ ngày: " . $p->f . " đến ngày: " . $p->t);

        $row = 6;
        $alphabet = range('A', 'Z');
        foreach ($products as $t => $column) {
            $sheet->setCellValue($alphabet[$t + 3] . '5', $column);
        }
        
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item['province_name']);
            $sheet->setCellValue("C" . $row, vietec_format_number2($item['total_amounts'], true));
            foreach ($products as $t => $column) {
                $sheet->setCellValue($alphabet[$t + 3] . $row, vietec_format_number2($item[$column], true));
            }

            $row++;
        }
        $sheet->getStyle('A5:J' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="report_sale_week.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Xuất excel Báo cáo công nợ
     */
    public function report_sale_debt(Request $request)
    {
        $inputFileName = 'templates/report_sale_debt.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $p = Report::params($request);
        $data = Report::report_sale_debt($p);
        $sheet->setCellValue("A3", "Tính đến thời điểm: " . $p->a);

        $row = 6;
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item->school_name);
            $sheet->setCellValue("C" . $row, vietec_format_number2($item->contract_value, true));
            $sheet->setCellValue("D" . $row, vietec_format_number2($item->payment_amount, true));
            $sheet->setCellValue("E" . $row, vietec_format_number2($item->received_money, true));
            $sheet->setCellValue("F" . $row, vietec_format_number2($item->debt, true));
            $sheet->setCellValue("G" . $row, $item->product_code);
            $sheet->setCellValue("H" . $row, $item->head_master_name);
            $sheet->setCellValue("I" . $row, $item->phone_number);
            $sheet->setCellValue("J" . $row, $item->accountant_name);
            $sheet->setCellValue("K" . $row, $item->accountant_phone);
            $sheet->setCellValue("L" . $row, $item->startDate);
            $sheet->setCellValue("M" . $row, $item->endDate);
            $sheet->setCellValue("N" . $row, $item->province_name);
            $row++;
        }
        $sheet->getStyle('A5:N' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="report_sale_week.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Xuất excel Danh sách các hợp đồng sắp hết hạn
     */
    public function report_sale_expire(Request $request)
    {
        $inputFileName = 'templates/report_sale_expire.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $p = Report::params($request);
        $data = Report::report_sale_expire($p);
        $sheet->setCellValue("A3", "Tính đến thời điểm: " . $p->a);

        $row = 6;
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item->school_name);
            $sheet->setCellValue("C" . $row, $item->startDate);
            $sheet->setCellValue("D" . $row, $item->endDate);
            $sheet->setCellValue("E" . $row, $item->product_code);
            $sheet->setCellValue("F" . $row, vietec_format_number2($item->contract_value, true));
            $sheet->setCellValue("G" . $row, vietec_format_number2($item->payment_amount, true));
            $sheet->setCellValue("H" . $row, vietec_format_number2($item->received_money, true));
            $sheet->setCellValue("I" . $row, vietec_format_number2($item->debt, true));
            $sheet->setCellValue("J" . $row, $item->head_master_name);
            $sheet->setCellValue("K" . $row, $item->phone_number);
            $sheet->setCellValue("L" . $row, $item->accountant_name);
            $sheet->setCellValue("M" . $row, $item->accountant_phone);
            $sheet->setCellValue("N" . $row, $item->province_name);
            $row++;
        }
        $sheet->getStyle('A5:N' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="report_sale_week.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Xuất excel báo cáo cuộc gọi khách hàng
     */
    public function report_call_center(Request $request)
    {
        $inputFileName = 'templates/report_call_center.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $p = Report::params($request);
        $data = Report::report_call_center($p);
        $sheet->setCellValue("A3", "Từ ngày: " . $p->f . " đến ngày: " . $p->t);

        $row = 6;
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item->SIP);
            $sheet->setCellValue("C" . $row, $item->FullName);
            $sheet->setCellValue("D" . $row, $item->total_calls);
            $sheet->setCellValue("E" . $row, round($item->total_times / 60, 1));
            $sheet->setCellValue("F" . $row, $item->qa);
            $sheet->setCellValue("G" . $row, $item->eos);
            $sheet->setCellValue("H" . $row, $item->pcgd);
            $sheet->setCellValue("I" . $row, $item->pms);
            $sheet->setCellValue("J" . $row, $item->khgd);
            $sheet->setCellValue("K" . $row, $item->total_calls - $item->qa - $item->eos - $item->pcgd - $item->khgd - $item->pms);
            $row++;
        }
        $sheet->getStyle('A5:K' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="report_call_center.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }
    /**
     * Xuất excel báo cáo doanh thu theo sản phẩm trong 3 năm gần nhất
     */
    public function export_sales(Request $request)
    {
        $inputFileName = 'templates/sales_three_year.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $data = Report::report_three_years($request);
        $row = 6;
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item->code);
            $sheet->setCellValue("C" . $row, $item->name);
            $sheet->setCellValue("D" . $row, vietec_format_number($item->two_year_ago ?: '0', true));
            $sheet->setCellValue("E" . $row, vietec_format_number($item->last_year ?: '0', true));
            $sheet->setCellValue("F" . $row, vietec_format_number($item->current_year ?: '0', true));
            $row++;
        }
        $sheet->getStyle('A5:F' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="sales_three_year.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }
    /**
     * Xuất excel báo cáo doanh thu theo team kinh doanh trong năm hiện tại
     */
    public function export_team_sales(Request $request)
    {
        $inputFileName = 'templates/team_sales.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $data = Report::report_current_years($request);

        $row = 6;
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item->name);
            $sheet->setCellValue("C" . $row, vietec_format_number($item->sale_1 ?: '0', true));
            $sheet->setCellValue("D" . $row, vietec_format_number($item->sale_2 ?: '0', true));
            $sheet->setCellValue("E" . $row, vietec_format_number($item->sale_3 ?: '0', true));
            $sheet->setCellValue("F" . $row, vietec_format_number($item->sale_4 ?: '0', true));
            $sheet->setCellValue("G" . $row, vietec_format_number($item->sale_5 ?: '0', true));
            $sheet->setCellValue("H" . $row, vietec_format_number($item->sale_6 ?: '0', true));
            $sheet->setCellValue("I" . $row, vietec_format_number($item->sale_7 ?: '0', true));
            $sheet->setCellValue("J" . $row, vietec_format_number($item->sale_8 ?: '0', true));
            $sheet->setCellValue("K" . $row, vietec_format_number($item->sale_9 ?: '0', true));
            $sheet->setCellValue("L" . $row, vietec_format_number($item->sale_10 ?: '0', true));
            $sheet->setCellValue("M" . $row, vietec_format_number($item->sale_11 ?: '0', true));
            $sheet->setCellValue("N" . $row, vietec_format_number($item->sale_12 ?: '0', true));
            $row++;
        }
        $sheet->getStyle('A5:N' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="team_sales.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }
    /**
     * Xuất excel Báo cáo doanh thu giữa tình hình thực tế và mục tiêu của từng team kinh doanh trong năm hiện tại
     */
    public function export_actual_sales_and_goal_sales(Request $request)
    {
        $inputFileName = 'templates/actual_sales_and_goal_sales.xls';
        $fileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($inputFileName);
        $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);

        $data = Report::report_actual_sales_and_goal_sales($request);

        $row = 6;
        foreach ($data as $key => $item) {
            $sheet->setCellValue("A" . $row, $key + 1);
            $sheet->setCellValue("B" . $row, $item->leader->representatives);
            $sheet->setCellValue("C" . $row, vietec_format_number($item->q1_goals ?: '0', true));
            $sheet->setCellValue("D" . $row, vietec_format_number($item->q1_sales ?: '0', true));
            $sheet->setCellValue("E" . $row, vietec_format_number($item->q2_goals ?: '0', true));
            $sheet->setCellValue("F" . $row, vietec_format_number($item->q2_sales ?: '0', true));
            $sheet->setCellValue("G" . $row, vietec_format_number($item->q3_goals ?: '0', true));
            $sheet->setCellValue("H" . $row, vietec_format_number($item->q3_sales ?: '0', true));
            $sheet->setCellValue("I" . $row, vietec_format_number($item->q4_goals ?: '0', true));
            $sheet->setCellValue("J" . $row, vietec_format_number($item->q4_sales ?: '0', true));
            $row++;
        }
        $sheet->getStyle('A5:J' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="actual_sales_and_goal_sales.xls"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $exception) {
            throw $exception;
        }
    }
}