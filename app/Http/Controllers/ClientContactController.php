<?php

namespace App\Http\Controllers;

use App\Models\CallCenter;
use App\Models\Client;
use App\Models\ClientContact;
use App\Models\Ticket;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ClientContactController extends Controller
{
    public function index(Request $request)
    {
        try {
            $query = ClientContact::select('*');
            if ($request->productId) {
                $query->where('product_id', $request->productId);
            }

            if ($request->provinceId) {
                $query->where('province_id', $request->provinceId);
            }

            $rs = $query->groupBy('client_id')
                ->orderBy('name')
                ->get();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('ClientContactController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function phoneNumberByClient(Request $request)
    {
        try {
            if (!$request->clientId) {
                return response()->json([], 400);
            }

            $query = ClientContact::where('client_id', $request->clientId);
            if ($request->productId) {
                $query->where('product_id', $request->productId);
            }

            $rs = $query->pluck('phone_number')
                ->toArray();

            return response()->json($rs);
        } catch (Exception $e) {
            Log::error('ClientContactController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function addPhoneNumberToClient(Request $request)
    {
        try {
            $validatedData = Validator::make($request->all(), [
                'phone_number' => 'required',
                'product_id' => 'required',
                'client_id' => 'required'
            ]);

            if ($validatedData->fails()) {
                return response()->json([], 400);
            }

            $check = ClientContact::where('client_id', $request->client_id)
                ->where('product_id', $request->product_id)
                ->where('phone_number', $request->phone_number)
                ->first();

            if ($check) {
                $check->person_in_charge = $request->person_in_charge;
                $check->save();

                return response()->json([
                    'status' => true,
                    'msg' => 'Cập nhật số điện thoại thành công!'
                ]);
            }

            $client = Client::find($request->client_id);
            if (!$client) {
                $msg = 'Không tìm thấy thông tin khách hàng với Id: ' . $request->client_id;
                Log::error($msg);
                return response()->json([
                    'status' => false,
                    'msg' => 'Dữ liệu không hợp lệ!'
                ]);
            };

            $model = new ClientContact();
            $model->name = $client->name;
            $model->province_id = $client->province_id;
            $model->district_id = $client->district_id;
            $model->name_upper = $client->name_upper;
            $model->address = $client->address;
            $model->head_master_name = $client->head_master_name;
            $model->school_id = $client->school_id;
            $model->client_id = $request->client_id;
            $model->product_id = $request->product_id;
            $model->phone_number = $request->phone_number;
            $model->person_in_charge = $request->person_in_charge;
            $model->save();

            return response()->json([
                'status' => true,
                'msg' => 'Thêm số điện thoại thành công!'
            ]);
        } catch (Exception $e) {
            Log::error('ClientContactController addPhoneNumberToClient: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function getClientInfoByPhone(Request $request)
    {
        try {
            $rs = ClientContact::with([
                'product',
                'province',
                'district',
                'tickets'
            ])
                ->where('phone_number', $request->phoneNumber)
                ->get();

            $data = [];
            if (!$rs) {
                return response()->json(null);
            }

            $clientId = $rs[0]->client_id;
            $allClientPhoneNumbers = ClientContact::where('client_id', $clientId)
                ->pluck('phone_number')
                ->toArray();
            $totalCalled = CallCenter::whereIn('RemoteNo', $allClientPhoneNumbers)->count();
            $latestTicket = Ticket::where('client_id', $clientId)
                ->orderBy('id', 'DESC')
                ->first();
            $data = [
                'name' => $rs[0]->name,
                'province' => $rs[0]->province->name,
                'district' => $rs[0]->district->name,
                'address' => $rs[0]->address,
                'head_master_name' => $rs[0]->head_master_name,
                'person_in_charge' => $rs[0]->person_in_charge,
                'totalCalled' => $totalCalled,
                'latestTicket' => $latestTicket ? [
                    'title' => $latestTicket->title,
                    'description' => $latestTicket->description,
                    'status' => Ticket::TICKET_STATUS[$latestTicket->status]
                ] : []
            ];
            foreach ($rs as $item) {
                $data['products'][] = [
                    'name' => $item->product->short_name,
                    'totalTicket' => $item->tickets->where('product_id', $item->product->id)->count()
                ];
            }

            return response()->json($data);
        } catch (Exception $e) {
            Log::error('ClientContactController getClientInfoByPhone: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }
}
